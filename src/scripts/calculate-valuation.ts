import { PrismaClient } from "@prisma/client"
import * as fs from "fs"
import * as path from "path"
import { organizationsDb } from "../modules/organizations/organizations.db"

// Import valuation functions from existing file
import * as xlsx from "xlsx"

const prisma = new PrismaClient()

type YearlyFinancials = {
	ebitda: number
	netSales: number
	operatingProfit: number
}

type Multipliers = {
	ebitda: { p25: number; p50: number; p75: number }
	netSales: { p25: number; p50: number; p75: number }
	opProfit: { p25: number; p50: number; p75: number }
}

type MVICScenarios = {
	low: number
	mid: number
	high: number
}

type DealStatsRow = {
	rowIndex: number
	ebitda?: number
	netSales?: number
	operatingProfit?: number
	mvicPrice?: number
	goodwillPPA?: number
}

type MultiplierWithSource = {
	value: number
	rowIndex: number
}

type ValuationResult = {
	organization: {
		id: string
		name: string
		naicsCode: string
	}
	financialData: {
		years: number[]
		hasRequiredData: boolean
		missingYears: number[]
	}
	brandStrength: {
		score: number | null
		evaluationId: string | null
		hasEvaluation: boolean
	}
	mvicScenarios: MVICScenarios
	mvicSource: "calculated" | "reported" | "mixed"
	brandValuation: MVICScenarios
	goodwillMultipliers: { p25: number; p50: number; p75: number }
	dealStatsFile: string | null
}

const OUTLIER_SENSITIVITY = 3

async function main() {
	const args = process.argv.slice(2)

	if (args.length === 0) {
		console.log("💎 Organization Valuation Calculator")
		console.log("\nUsage:")
		console.log(
			"  npm run valuation:calculate <orgId|orgName> [--use-reported-mvic]     - Calculate valuation for organization"
		)
		console.log(
			"  npm run valuation:calculate-all [--use-reported-mvic]                - Calculate valuations for all organizations"
		)
		console.log(
			"  npm run valuation:update <orgId|orgName> [--use-reported-mvic]        - Calculate and update valuation in database"
		)
		console.log(
			"  npm run valuation:update-all [--use-reported-mvic]                   - Calculate and update all organization valuations"
		)
		console.log(
			"  npm run valuation:list                                              - List organizations with valuations"
		)
		console.log(
			"  npm run valuation:dealstats                                         - List available DealStats files"
		)
		console.log("\nMVIC Options:")
		console.log(
			"  --use-reported-mvic    Use reported MVIC values from financial data when available"
		)
		console.log(
			"                         Falls back to calculated MVIC if reported values don't exist"
		)
		console.log(
			"  (default)              Calculate MVIC using industry multipliers from DealStats"
		)
		console.log("\nExamples:")
		console.log("  npm run valuation:calculate 'Tech Corp'")
		console.log("  npm run valuation:calculate 'Tech Corp' --use-reported-mvic")
		console.log("  npm run valuation:calculate abc123-def456")
		console.log("\nRequirements:")
		console.log("  • Organization must have NAICS code")
		console.log("  • Corresponding {NAICSCODE}.xlsx file must exist in root directory")
		console.log("  • Organization must have at least 3 years of financial data")
		console.log("  • Organization should have completed brand strength evaluation")
		return
	}

	const command = args[0]
	const useReportedMvic = args.includes("--use-reported-mvic")

	if (command === "calculate") {
		const orgIdentifier = args[1]
		if (!orgIdentifier) {
			console.error("❌ Please provide an organization ID or name")
			process.exit(1)
		}
		await calculateValuationForOrganization(orgIdentifier, false, useReportedMvic)
	} else if (command === "calculate-all") {
		await calculateValuationsForAllOrganizations(false, useReportedMvic)
	} else if (command === "update") {
		const orgIdentifier = args[1]
		if (!orgIdentifier) {
			console.error("❌ Please provide an organization ID or name")
			process.exit(1)
		}
		await calculateValuationForOrganization(orgIdentifier, true, useReportedMvic)
	} else if (command === "update-all") {
		await calculateValuationsForAllOrganizations(true, useReportedMvic)
	} else if (command === "list") {
		await listOrganizationValuations()
	} else if (command === "dealstats") {
		await listDealStatsFiles()
	} else {
		console.error("❌ Unknown command:", command)
		process.exit(1)
	}
}

async function findOrganization(identifier: string) {
	// Try to find by ID first (if it looks like a UUID)
	if (identifier.length === 36 && identifier.includes("-")) {
		const org = await prisma.organization.findUnique({
			where: { id: identifier },
			include: {
				yearData: {
					orderBy: { year: "desc" },
				},
				evaluations: {
					include: {
						responses: {
							include: {
								selectedOption: true,
								selectedOptions: true,
								surveyQuestion: {
									include: {
										questionOption: true,
									},
								},
							},
						},
					},
					orderBy: { brandStrengthScore: "desc" },
				},
			},
		})
		if (org) return org
	}

	// Try to find by name
	const org = await prisma.organization.findFirst({
		where: { name: { contains: identifier, mode: "insensitive" } },
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
			evaluations: {
				include: {
					responses: {
						include: {
							selectedOption: true,
							selectedOptions: true,
							surveyQuestion: {
								include: {
									questionOption: true,
								},
							},
						},
					},
				},
				orderBy: { brandStrengthScore: "desc" },
			},
		},
	})

	return org
}

function findDealStatsFile(naicsCode: string): string | null {
	const possiblePaths = [
		path.join(process.cwd(), `${naicsCode}.xlsx`),
		path.join(process.cwd(), "src", `${naicsCode}.xlsx`),
		path.join(process.cwd(), "data", `${naicsCode}.xlsx`),
	]

	for (const filePath of possiblePaths) {
		if (fs.existsSync(filePath)) {
			return filePath
		}
	}

	return null
}

async function listDealStatsFiles() {
	console.log("📊 Available DealStats Files")
	console.log("=".repeat(50))

	const searchPaths = [
		process.cwd(),
		path.join(process.cwd(), "src"),
		path.join(process.cwd(), "data"),
	]

	const foundFiles: string[] = []

	for (const searchPath of searchPaths) {
		if (fs.existsSync(searchPath)) {
			const files = fs.readdirSync(searchPath)
			const xlsxFiles = files.filter(
				file => file.endsWith(".xlsx") && /^\d+\.xlsx$/.test(file)
			)

			for (const file of xlsxFiles) {
				const fullPath = path.join(searchPath, file)
				const naicsCode = file.replace(".xlsx", "")
				foundFiles.push(`${naicsCode} -> ${fullPath}`)
			}
		}
	}

	if (foundFiles.length === 0) {
		console.log("📭 No DealStats files found")
		console.log("\n💡 DealStats files should be named {NAICSCODE}.xlsx")
		console.log("   Example: 522110.xlsx, 332999.xlsx")
	} else {
		foundFiles.forEach(file => console.log(`   📄 ${file}`))
		console.log(`\n📊 Total: ${foundFiles.length} DealStats files found`)
	}

	// Also show organizations and their NAICS codes
	console.log("\n🏢 Organizations and their NAICS codes:")
	console.log("-".repeat(50))

	const organizations = await organizationsDb.getOrganizations()
	for (const org of organizations) {
		const hasFile = findDealStatsFile(org.NAICSCode) !== null
		const status = hasFile ? "✅" : "❌"
		console.log(`   ${status} ${org.name.padEnd(30)} NAICS: ${org.NAICSCode}`)
	}
}

async function calculateValuationForOrganization(
	identifier: string,
	updateDatabase: boolean = false,
	useReportedMvic: boolean = false
) {
	try {
		console.log(
			`\n💎 ${
				updateDatabase ? "Calculating and updating" : "Calculating"
			} valuation for: ${identifier}`
		)

		if (useReportedMvic) {
			console.log("🔧 Using reported MVIC values when available")
		}

		// Find organization
		const organization = await findOrganization(identifier)
		if (!organization) {
			console.error(`❌ Organization not found: ${identifier}`)
			console.log("💡 Use 'npm run valuation:list' to see available organizations")
			return null
		}

		console.log(`🏢 Organization: ${organization.name}`)
		console.log(`🏷️  NAICS Code: ${organization.NAICSCode}`)

		// Find DealStats file
		const dealStatsFile = findDealStatsFile(organization.NAICSCode)
		if (!dealStatsFile) {
			console.error(`❌ DealStats file not found for NAICS code: ${organization.NAICSCode}`)
			console.log(`💡 Expected file: ${organization.NAICSCode}.xlsx`)
			console.log("💡 Use 'npm run valuation:dealstats' to see available files")
			return null
		}

		console.log(`📊 Using DealStats file: ${dealStatsFile}`)

		// Check financial data
		const yearData = organization.yearData || []
		if (yearData.length < 3) {
			console.error(
				`❌ Insufficient financial data. Found ${yearData.length} years, need at least 3`
			)
			console.log("💡 Use 'npm run financial:add' to add more financial data")
			return null
		}

		// Get most recent 3 years
		const recentYears = yearData.slice(0, 3)
		console.log(
			`📈 Using financial data from years: ${recentYears.map(y => y.year).join(", ")}`
		)

		// Check for brand strength evaluation
		const evaluation = organization.evaluations?.[0] // Most recent evaluation with highest score
		let brandStrengthScore = 0.5 // Default if no evaluation

		if (evaluation && evaluation.brandStrengthScore) {
			brandStrengthScore = evaluation.brandStrengthScore / 1000 // Convert to 0-1 scale
			console.log(
				`🎯 Brand Strength Score: ${evaluation.brandStrengthScore}/1000 (${(
					brandStrengthScore * 100
				).toFixed(1)}%)`
			)
		} else {
			console.log(`⚠️  No brand strength evaluation found, using default: 50%`)
		}

		// Prepare financial data for calculation
		const financials: YearlyFinancials[] = recentYears.map(yd => ({
			ebitda: yd.EBITDA || 0,
			netSales: yd.netSales || 0,
			operatingProfit: yd.OPM ? (yd.netSales * yd.OPM) / 100 : yd.EBITDA || 0, // Calculate operating profit from OPM or use EBITDA
		}))

		// Validate financial data
		console.log("📊 Financial data validation:")
		financials.forEach((f, index) => {
			const year = recentYears[index].year
			console.log(
				`   ${year}: EBITDA: $${f.ebitda.toLocaleString()}, Net Sales: $${f.netSales.toLocaleString()}, Op Profit: $${f.operatingProfit.toLocaleString()}`
			)

			if (f.ebitda <= 0 || f.netSales <= 0 || f.operatingProfit <= 0) {
				console.warn(`   ⚠️  Warning: Year ${year} has zero or negative values`)
			}
		})

		// Calculate valuation
		const result = await performValuationCalculation(
			organization,
			financials,
			brandStrengthScore,
			dealStatsFile,
			evaluation?.id || null,
			useReportedMvic
		)

		// Display results
		displayValuationResults(result)

		// Update database if requested
		if (updateDatabase && result.brandValuation.mid > 0) {
			await prisma.organization.update({
				where: { id: organization.id },
				data: { valuation: result.brandValuation.mid },
			})
			console.log("💾 Valuation updated in database")
		}

		return result
	} catch (error) {
		console.error("❌ Error calculating valuation:", error)
		return null
	}
}

async function performValuationCalculation(
	organization: any,
	financials: YearlyFinancials[],
	brandStrengthScore: number,
	dealStatsFile: string,
	evaluationId: string | null,
	useReportedMvic: boolean = false
): Promise<ValuationResult> {
	// Load multipliers from DealStats file
	const { multipliers, goodwillMultipliers } = getAllMultipliersFromDealStats([dealStatsFile])

	// Calculate MVIC scenarios - either from reported values or calculated
	let mvicScenarios: MVICScenarios
	let mvicSource: "calculated" | "reported" | "mixed"

	if (useReportedMvic) {
		const reportedMvicResult = calculateReportedMVIC(organization.yearData)
		if (reportedMvicResult.hasReportedData) {
			mvicScenarios = reportedMvicResult.scenarios
			mvicSource = reportedMvicResult.source
			console.log(`📊 Using ${mvicSource} MVIC values`)
		} else {
			console.log("⚠️  No reported MVIC data found, falling back to calculated MVIC")
			mvicScenarios = calculateProjectedMVIC(financials, multipliers)
			mvicSource = "calculated"
		}
	} else {
		mvicScenarios = calculateProjectedMVIC(financials, multipliers)
		mvicSource = "calculated"
	}

	// Calculate goodwill percentiles with fallback values
	let goodwillPercentiles = {
		p25: 0.15, // Default fallback values
		p50: 0.22,
		p75: 0.31,
	}

	if (goodwillMultipliers.length >= 3) {
		goodwillPercentiles = {
			p25: getPercentile(goodwillMultipliers, 25),
			p50: getPercentile(goodwillMultipliers, 50),
			p75: getPercentile(goodwillMultipliers, 75),
		}
		console.log(
			`📊 Using calculated goodwill percentiles from ${goodwillMultipliers.length} data points`
		)
	} else {
		console.log(
			`⚠️  Insufficient goodwill data (${goodwillMultipliers.length} points), using default percentiles`
		)
	}

	// Calculate brand valuation
	const brandValuation = calculateBrandValuation(
		mvicScenarios,
		brandStrengthScore,
		goodwillPercentiles
	)

	return {
		organization: {
			id: organization.id,
			name: organization.name,
			naicsCode: organization.NAICSCode,
		},
		financialData: {
			years: financials.map((_, index) => organization.yearData[index].year),
			hasRequiredData: financials.length >= 3,
			missingYears: [],
		},
		brandStrength: {
			score: brandStrengthScore * 1000, // Convert back to 0-1000 scale for display
			evaluationId,
			hasEvaluation: evaluationId !== null,
		},
		mvicScenarios,
		mvicSource,
		brandValuation,
		goodwillMultipliers: goodwillPercentiles,
		dealStatsFile,
	}
}

function displayValuationResults(result: ValuationResult) {
	console.log("\n" + "=".repeat(60))
	console.log("💎 VALUATION RESULTS")
	console.log("=".repeat(60))

	console.log(`🏢 Organization: ${result.organization.name}`)
	console.log(`🏷️  NAICS Code: ${result.organization.naicsCode}`)
	console.log(`📊 DealStats File: ${path.basename(result.dealStatsFile || "")}`)

	console.log(`\n📈 MVIC Scenarios (${result.mvicSource}):`)
	if (result.mvicSource === "calculated") {
		console.log(`   Low (25th percentile):  ${formatMillions(result.mvicScenarios.low)}`)
		console.log(`   Mid (50th percentile):  ${formatMillions(result.mvicScenarios.mid)}`)
		console.log(`   High (75th percentile): ${formatMillions(result.mvicScenarios.high)}`)
	} else if (result.mvicSource === "reported") {
		console.log(`   Low (-15%):  ${formatMillions(result.mvicScenarios.low)}`)
		console.log(`   Mid (reported):  ${formatMillions(result.mvicScenarios.mid)}`)
		console.log(`   High (+25%): ${formatMillions(result.mvicScenarios.high)}`)
	} else {
		console.log(`   Low (mixed):  ${formatMillions(result.mvicScenarios.low)}`)
		console.log(`   Mid (mixed):  ${formatMillions(result.mvicScenarios.mid)}`)
		console.log(`   High (mixed): ${formatMillions(result.mvicScenarios.high)}`)
	}

	console.log("\n🎯 Brand Strength:")
	if (result.brandStrength.hasEvaluation) {
		console.log(
			`   Score: ${result.brandStrength.score?.toFixed(0)}/1000 (${(
				(result.brandStrength.score || 0) / 10
			).toFixed(1)}%)`
		)
		console.log(`   Evaluation ID: ${result.brandStrength.evaluationId}`)
	} else {
		console.log(`   Score: Default 500/1000 (50%) - No evaluation found`)
	}

	console.log("\n💰 Goodwill Multipliers:")
	console.log(`   Low:  ${result.goodwillMultipliers.p25.toFixed(2)}x`)
	console.log(`   Mid:  ${result.goodwillMultipliers.p50.toFixed(2)}x`)
	console.log(`   High: ${result.goodwillMultipliers.p75.toFixed(2)}x`)

	console.log("\n💎 BRAND VALUATION:")
	console.log(`   Low:  ${formatMillions(result.brandValuation.low)}`)
	console.log(`   Mid:  ${formatMillions(result.brandValuation.mid)}`)
	console.log(`   High: ${formatMillions(result.brandValuation.high)}`)

	console.log("\n💡 Brand Valuation as % of MVIC:")
	console.log(
		`   Low:  ${brandPercentageOfMVIC(result.brandValuation.low, result.mvicScenarios.low)}`
	)
	console.log(
		`   Mid:  ${brandPercentageOfMVIC(result.brandValuation.mid, result.mvicScenarios.mid)}`
	)
	console.log(
		`   High: ${brandPercentageOfMVIC(result.brandValuation.high, result.mvicScenarios.high)}`
	)
}

async function calculateValuationsForAllOrganizations(
	updateDatabase: boolean = false,
	useReportedMvic: boolean = false
) {
	try {
		console.log(
			`\n💎 ${
				updateDatabase ? "Calculating and updating" : "Calculating"
			} valuations for all organizations...`
		)

		const organizations = await organizationsDb.getOrganizations()

		if (organizations.length === 0) {
			console.log("📭 No organizations found")
			return
		}

		console.log(`🏢 Found ${organizations.length} organizations`)

		const results = []

		for (let i = 0; i < organizations.length; i++) {
			const org = organizations[i]
			console.log(`\n[${i + 1}/${organizations.length}] Processing: ${org.name}`)

			try {
				const result = await calculateValuationForOrganization(
					org.id,
					updateDatabase,
					useReportedMvic
				)
				results.push({
					id: org.id,
					name: org.name,
					naicsCode: org.NAICSCode,
					result,
					success: result !== null,
				})
			} catch (error) {
				console.error(`❌ Failed to process organization ${org.id}:`, error)
				results.push({
					id: org.id,
					name: org.name,
					naicsCode: org.NAICSCode,
					result: null,
					success: false,
				})
			}
		}

		// Summary
		console.log("\n" + "=".repeat(60))
		console.log("📊 SUMMARY")
		console.log("=".repeat(60))

		const successful = results.filter(r => r.success)
		const failed = results.filter(r => !r.success)

		console.log(`✅ Successfully processed: ${successful.length}`)
		console.log(`❌ Failed: ${failed.length}`)

		if (successful.length > 0) {
			const avgValuation =
				successful.reduce((sum, r) => sum + (r.result?.brandValuation.mid || 0), 0) /
				successful.length
			const maxValuation = Math.max(...successful.map(r => r.result?.brandValuation.mid || 0))
			const minValuation = Math.min(...successful.map(r => r.result?.brandValuation.mid || 0))

			console.log(`📈 Average Brand Valuation: ${formatMillions(avgValuation)}`)
			console.log(`🏆 Highest Brand Valuation: ${formatMillions(maxValuation)}`)
			console.log(`📉 Lowest Brand Valuation: ${formatMillions(minValuation)}`)
		}

		if (failed.length > 0) {
			console.log("\n❌ Failed Organizations:")
			failed.forEach(f => {
				console.log(`   • ${f.name} (NAICS: ${f.naicsCode})`)
			})
		}
	} catch (error) {
		console.error("❌ Error processing organizations:", error)
		throw error
	}
}

async function listOrganizationValuations() {
	try {
		console.log("\n💎 Organization Valuations")
		console.log("=".repeat(100))

		const organizations = await organizationsDb.getOrganizations()

		if (organizations.length === 0) {
			console.log("📭 No organizations found")
			return
		}

		console.log(
			`${"Organization".padEnd(25)} ${"NAICS".padEnd(8)} ${"Years".padEnd(
				8
			)} ${"Valuation".padEnd(15)} ${"Brand Score".padEnd(12)} ${"Status".padEnd(15)}`
		)
		console.log("-".repeat(100))

		for (const org of organizations) {
			const orgName = org.name.substring(0, 23).padEnd(25)
			const naics = org.NAICSCode.padEnd(8)
			const yearCount = `${org.yearData?.length || 0}`.padEnd(8)
			const valuation = org.valuation
				? `${formatMillions(org.valuation)}`.padEnd(15)
				: "Not calc".padEnd(15)

			// Check if organization has evaluation
			const evaluation = await prisma.evaluation.findFirst({
				where: { organizationId: org.id },
				orderBy: { brandStrengthScore: "desc" },
			})

			const brandScore = evaluation?.brandStrengthScore
				? `${evaluation.brandStrengthScore}/1000`.padEnd(12)
				: "No eval".padEnd(12)

			// Determine status
			let status = "❌ Missing data"
			const hasFinancialData = (org.yearData?.length || 0) >= 3
			const hasDealStats = findDealStatsFile(org.NAICSCode) !== null
			const hasEvaluation = evaluation !== null

			if (hasFinancialData && hasDealStats && hasEvaluation) {
				status = "✅ Ready"
			} else if (hasFinancialData && hasDealStats) {
				status = "⚠️  No evaluation"
			} else if (hasFinancialData) {
				status = "⚠️  No DealStats"
			} else if (hasDealStats) {
				status = "⚠️  No financials"
			}

			console.log(`${orgName} ${naics} ${yearCount} ${valuation} ${brandScore} ${status}`)
		}

		const withValuations = organizations.filter(org => org.valuation !== null)
		const ready = organizations.filter(org => {
			const hasFinancialData = (org.yearData?.length || 0) >= 3
			const hasDealStats = findDealStatsFile(org.NAICSCode) !== null
			return hasFinancialData && hasDealStats
		})

		console.log("-".repeat(100))
		console.log(
			`📊 Total: ${organizations.length} | 💎 With Valuations: ${withValuations.length} | ✅ Ready for Valuation: ${ready.length}`
		)
	} catch (error) {
		console.error("❌ Error listing valuations:", error)
	}
}

// Import and adapt functions from valuations.ts
function formatMillions(value: number): string {
	const millions = value / 1_000_000
	return `${millions.toLocaleString("en-US", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	})}M`
}

function brandPercentageOfMVIC(brandValue: number, mvic: number): string {
	return `${((brandValue / mvic) * 100).toFixed(2)}%`
}

// Core valuation calculation functions adapted from valuations.ts
function getAllMultipliersFromDealStats(filePaths: string[]): {
	multipliers: Multipliers
	goodwillMultipliers: number[]
	excludedRowIndices: Set<number>
} {
	console.log("📊 Loading DealStats data...")
	const allRows = loadAllDealStatsRows(filePaths)
	console.log(`   Loaded ${allRows.length} valid rows from DealStats`)

	if (allRows.length === 0) {
		throw new Error("No valid data found in DealStats files")
	}

	// Extract multipliers for all metrics INCLUDING goodwill
	const ebitdaMultipliers = extractMultipliersWithSource(allRows, "EBITDA")
	const netSalesMultipliers = extractMultipliersWithSource(allRows, "Net Sales")
	const opProfitMultipliers = extractMultipliersWithSource(allRows, "Operating Profit")
	const goodwillMultipliersWithSource = extractGoodwillMultipliersWithSource(allRows)

	console.log(`   EBITDA multipliers: ${ebitdaMultipliers.length}`)
	console.log(`   Net Sales multipliers: ${netSalesMultipliers.length}`)
	console.log(`   Operating Profit multipliers: ${opProfitMultipliers.length}`)
	console.log(`   Goodwill multipliers: ${goodwillMultipliersWithSource.length}`)

	// Identify outlier rows across ALL metrics INCLUDING goodwill
	const allOutlierRowIndices = new Set<number>()

	// Check for outliers in each metric and combine the outlier row indices
	const ebitdaOutliers = identifyOutlierRows(ebitdaMultipliers)
	const netSalesOutliers = identifyOutlierRows(netSalesMultipliers)
	const opProfitOutliers = identifyOutlierRows(opProfitMultipliers)
	const goodwillOutliers = identifyOutlierRows(goodwillMultipliersWithSource)

	// Union all outlier row indices
	ebitdaOutliers.forEach(idx => allOutlierRowIndices.add(idx))
	netSalesOutliers.forEach(idx => allOutlierRowIndices.add(idx))
	opProfitOutliers.forEach(idx => allOutlierRowIndices.add(idx))
	goodwillOutliers.forEach(idx => allOutlierRowIndices.add(idx))

	// Filter out outlier rows from ALL metrics
	const filteredEbitda = ebitdaMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
		.filter(v => !isNaN(v) && isFinite(v) && v > 0)

	const filteredNetSales = netSalesMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
		.filter(v => !isNaN(v) && isFinite(v) && v > 0)

	let filteredOpProfit = opProfitMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
		.filter(v => !isNaN(v) && isFinite(v) && v > 0)

	const filteredGoodwill = goodwillMultipliersWithSource
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
		.filter(v => !isNaN(v) && isFinite(v) && v > 0)

	console.log(
		`   After filtering: EBITDA: ${filteredEbitda.length}, Net Sales: ${filteredNetSales.length}, Op Profit: ${filteredOpProfit.length}, Goodwill: ${filteredGoodwill.length}`
	)

	// Validate we have enough data - require at least EBITDA and Net Sales
	if (filteredEbitda.length === 0 || filteredNetSales.length === 0) {
		throw new Error(
			"Insufficient valid multiplier data after filtering - need at least EBITDA and Net Sales"
		)
	}

	// If no operating profit data, use EBITDA as fallback
	if (filteredOpProfit.length === 0) {
		console.log("⚠️  No Operating Profit data found, using EBITDA multipliers as fallback")
		filteredOpProfit.push(...filteredEbitda)
	}

	const multipliers = {
		ebitda: {
			p25: getPercentile(filteredEbitda, 25),
			p50: getPercentile(filteredEbitda, 50),
			p75: getPercentile(filteredEbitda, 75),
		},
		netSales: {
			p25: getPercentile(filteredNetSales, 25),
			p50: getPercentile(filteredNetSales, 50),
			p75: getPercentile(filteredNetSales, 75),
		},
		opProfit: {
			p25: getPercentile(filteredOpProfit, 25),
			p50: getPercentile(filteredOpProfit, 50),
			p75: getPercentile(filteredOpProfit, 75),
		},
	}

	// Validate multipliers are not NaN
	Object.entries(multipliers).forEach(([metric, values]) => {
		Object.entries(values).forEach(([percentile, value]) => {
			if (isNaN(value) || !isFinite(value)) {
				throw new Error(`Invalid ${metric} ${percentile} multiplier: ${value}`)
			}
		})
	})

	console.log("📊 Calculated multipliers:")
	console.log(
		`   EBITDA: P25=${multipliers.ebitda.p25.toFixed(2)}, P50=${multipliers.ebitda.p50.toFixed(
			2
		)}, P75=${multipliers.ebitda.p75.toFixed(2)}`
	)
	console.log(
		`   Net Sales: P25=${multipliers.netSales.p25.toFixed(
			2
		)}, P50=${multipliers.netSales.p50.toFixed(2)}, P75=${multipliers.netSales.p75.toFixed(2)}`
	)
	console.log(
		`   Op Profit: P25=${multipliers.opProfit.p25.toFixed(
			2
		)}, P50=${multipliers.opProfit.p50.toFixed(2)}, P75=${multipliers.opProfit.p75.toFixed(2)}`
	)

	return {
		multipliers,
		goodwillMultipliers: filteredGoodwill,
		excludedRowIndices: allOutlierRowIndices,
	}
}

function loadAllDealStatsRows(filePaths: string[]): DealStatsRow[] {
	const allRows: DealStatsRow[] = []
	let globalRowIndex = 0

	for (const filePath of filePaths) {
		try {
			console.log(`   Loading file: ${path.basename(filePath)}`)
			const workbook = xlsx.readFile(filePath)
			const sheet = workbook.Sheets[workbook.SheetNames[0]]
			const data = xlsx.utils.sheet_to_json<any>(sheet)

			console.log(`   Found ${data.length} rows in Excel file`)

			// Log column headers for debugging
			if (data.length > 0) {
				const headers = Object.keys(data[0])
				console.log(`   Column headers: ${headers.join(", ")}`)
			}

			let validRows = 0
			for (const row of data) {
				// Try different possible column name variations
				const ebitda =
					parseFloat(row["EBITDA"] || row["Ebitda"] || row["ebitda"]) || undefined
				const netSales =
					parseFloat(
						row["Net Sales"] || row["NetSales"] || row["Revenue"] || row["Sales"]
					) || undefined
				const operatingProfit =
					parseFloat(
						row["Operating Profit"] ||
							row["OperatingProfit"] ||
							row["Op Profit"] ||
							row["EBIT"] ||
							row["Operating Income"] ||
							row["Operating Margin"] // This might be a percentage, we'll handle it differently
					) || undefined
				const mvicPrice =
					parseFloat(
						row["MVIC Price"] || row["MVICPrice"] || row["MVIC"] || row["Price"]
					) || undefined
				const goodwillPPA =
					parseFloat(row["Goodwill PPA"] || row["GoodwillPPA"] || row["Goodwill"]) ||
					undefined

				const dealStatsRow: DealStatsRow = {
					rowIndex: globalRowIndex++,
					ebitda,
					netSales,
					operatingProfit,
					mvicPrice,
					goodwillPPA,
				}

				// Only include rows that have valid MVIC Price
				if (dealStatsRow.mvicPrice && dealStatsRow.mvicPrice > 0) {
					allRows.push(dealStatsRow)
					validRows++
				}
			}

			console.log(`   Valid rows with MVIC Price: ${validRows}`)
		} catch (error) {
			console.warn(`Failed to load DealStats file ${filePath}:`, error)
		}
	}

	return allRows
}

function extractMultipliersWithSource(
	rows: DealStatsRow[],
	columnName: string
): MultiplierWithSource[] {
	const multipliers: MultiplierWithSource[] = []

	for (const row of rows) {
		let metricValue: number | undefined

		switch (columnName) {
			case "EBITDA":
				metricValue = row.ebitda
				break
			case "Net Sales":
				metricValue = row.netSales
				break
			case "Operating Profit":
				metricValue = row.operatingProfit
				break
			default:
				continue
		}

		if (metricValue && row.mvicPrice && metricValue > 0) {
			const multiplier = row.mvicPrice / metricValue
			multipliers.push({
				value: multiplier,
				rowIndex: row.rowIndex,
			})
		}
	}

	return multipliers
}

function extractGoodwillMultipliersWithSource(rows: DealStatsRow[]): MultiplierWithSource[] {
	const multipliers: MultiplierWithSource[] = []

	for (const row of rows) {
		if (row.goodwillPPA && row.mvicPrice && row.mvicPrice > 0) {
			const multiplier = row.goodwillPPA / row.mvicPrice
			multipliers.push({
				value: multiplier,
				rowIndex: row.rowIndex,
			})
		}
	}

	return multipliers
}

function identifyOutlierRows(multipliersWithSource: MultiplierWithSource[]): Set<number> {
	if (multipliersWithSource.length < 4) return new Set()

	const values = multipliersWithSource.map(m => m.value)
	const sorted = [...values].sort((a, b) => a - b)
	const q1 = getPercentile(sorted, 25)
	const q3 = getPercentile(sorted, 75)
	const iqr = q3 - q1
	const lowerBound = q1 - OUTLIER_SENSITIVITY * iqr
	const upperBound = q3 + OUTLIER_SENSITIVITY * iqr

	const outlierRowIndices = new Set<number>()

	for (const multiplier of multipliersWithSource) {
		if (multiplier.value < lowerBound || multiplier.value > upperBound) {
			outlierRowIndices.add(multiplier.rowIndex)
		}
	}

	return outlierRowIndices
}

function getPercentile(data: number[], percentile: number): number {
	const sorted = [...data].sort((a, b) => a - b)
	const index = (percentile / 100) * (sorted.length - 1)
	const lower = Math.floor(index)
	const upper = Math.ceil(index)

	if (lower === upper) return sorted[lower]
	return sorted[lower] + (sorted[upper] - sorted[lower]) * (index - lower)
}

function calculateProjectedMVIC(
	financials: YearlyFinancials[],
	multipliers: Multipliers
): MVICScenarios {
	// Weighted average with most recent year weighted most heavily (3, 2, 1)
	const weights = [3, 2, 1] // Most recent to oldest
	const totalWeight = weights.reduce((sum, w) => sum + w, 0)

	function weightedAvgMVIC(
		metric: keyof YearlyFinancials,
		multi: (typeof multipliers)[keyof Multipliers]
	) {
		const mvicValues = financials.map(f => f[metric]!)

		// Validate financial values
		for (let i = 0; i < mvicValues.length; i++) {
			if (isNaN(mvicValues[i]) || !isFinite(mvicValues[i]) || mvicValues[i] <= 0) {
				console.warn(`   ⚠️  Invalid ${metric} value for year ${i}: ${mvicValues[i]}`)
				mvicValues[i] = 1 // Use minimal positive value to avoid NaN
			}
		}

		const mvicP25 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p25 * weights[index], 0) /
			totalWeight
		const mvicP50 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p50 * weights[index], 0) /
			totalWeight
		const mvicP75 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p75 * weights[index], 0) /
			totalWeight

		return { p25: mvicP25, p50: mvicP50, p75: mvicP75 }
	}

	const ebitda = weightedAvgMVIC("ebitda", multipliers.ebitda)
	const sales = weightedAvgMVIC("netSales", multipliers.netSales)
	const opProfit = weightedAvgMVIC("operatingProfit", multipliers.opProfit)

	console.log("📊 MVIC calculations by metric:")
	console.log(
		`   EBITDA: P25=${formatMillions(ebitda.p25)}, P50=${formatMillions(
			ebitda.p50
		)}, P75=${formatMillions(ebitda.p75)}`
	)
	console.log(
		`   Net Sales: P25=${formatMillions(sales.p25)}, P50=${formatMillions(
			sales.p50
		)}, P75=${formatMillions(sales.p75)}`
	)
	console.log(
		`   Op Profit: P25=${formatMillions(opProfit.p25)}, P50=${formatMillions(
			opProfit.p50
		)}, P75=${formatMillions(opProfit.p75)}`
	)

	// Simple average across the three metrics
	const avg = (arr: number[]) => {
		const validValues = arr.filter(v => !isNaN(v) && isFinite(v) && v > 0)
		if (validValues.length === 0) return 0
		return validValues.reduce((a, b) => a + b, 0) / validValues.length
	}

	const result = {
		low: avg([ebitda.p25, sales.p25, opProfit.p25]),
		mid: avg([ebitda.p50, sales.p50, opProfit.p50]),
		high: avg([ebitda.p75, sales.p75, opProfit.p75]),
	}

	// Validate final MVIC values
	Object.entries(result).forEach(([scenario, value]) => {
		if (isNaN(value) || !isFinite(value) || value <= 0) {
			console.error(`❌ Invalid MVIC ${scenario} value: ${value}`)
			throw new Error(`Invalid MVIC calculation resulted in ${scenario}: ${value}`)
		}
	})

	return result
}

function calculateBrandValuation(
	mvicScenarios: MVICScenarios,
	bsi: number,
	goodwillPercentiles: { p25: number; p50: number; p75: number }
) {
	// Validate inputs
	if (isNaN(bsi) || !isFinite(bsi) || bsi < 0 || bsi > 1) {
		console.warn(`⚠️  Invalid brand strength index: ${bsi}, using 0.5`)
		bsi = 0.5
	}

	console.log(`📊 Brand valuation calculation:`)
	console.log(`   Brand Strength Index: ${(bsi * 100).toFixed(1)}%`)
	console.log(
		`   Goodwill multipliers: P25=${goodwillPercentiles.p25.toFixed(
			3
		)}, P50=${goodwillPercentiles.p50.toFixed(3)}, P75=${goodwillPercentiles.p75.toFixed(3)}`
	)

	const result = {
		low: mvicScenarios.low * bsi * goodwillPercentiles.p25,
		mid: mvicScenarios.mid * bsi * goodwillPercentiles.p50,
		high: mvicScenarios.high * bsi * goodwillPercentiles.p75,
	}

	// Validate results
	Object.entries(result).forEach(([scenario, value]) => {
		if (isNaN(value) || !isFinite(value) || value < 0) {
			console.error(`❌ Invalid brand valuation ${scenario}: ${value}`)
			throw new Error(`Invalid brand valuation calculation resulted in ${scenario}: ${value}`)
		}
	})

	return result
}

function calculateReportedMVIC(yearData: any[]): {
	hasReportedData: boolean
	scenarios: MVICScenarios
	source: "reported" | "mixed"
} {
	// Get the most recent 3 years of data
	const recentYears = yearData.slice(0, 3)

	// Check if we have MVIC data for all years
	const mvicValues = recentYears.map(yd => yd.MVIC).filter(mvic => mvic && mvic > 0)

	if (mvicValues.length === 0) {
		return {
			hasReportedData: false,
			scenarios: { low: 0, mid: 0, high: 0 },
			source: "reported",
		}
	}

	// If we have some but not all MVIC values, it's mixed
	const source: "reported" | "mixed" =
		mvicValues.length === recentYears.length ? "reported" : "mixed"

	// Calculate weighted average of reported MVIC values
	// Use weights [3, 2, 1] for most recent to oldest, but only for available data
	const weights = [3, 2, 1]
	let totalWeightedValue = 0
	let totalWeight = 0

	for (let i = 0; i < recentYears.length; i++) {
		const mvic = recentYears[i].MVIC
		if (mvic && mvic > 0) {
			totalWeightedValue += mvic * weights[i]
			totalWeight += weights[i]
		}
	}

	const weightedAverage = totalWeight > 0 ? totalWeightedValue / totalWeight : 0

	if (weightedAverage <= 0) {
		return {
			hasReportedData: false,
			scenarios: { low: 0, mid: 0, high: 0 },
			source,
		}
	}

	// For reported MVIC, we'll create scenarios based on the weighted average
	// Apply some variance to create low/mid/high scenarios
	// Low: -15%, Mid: actual, High: +25%
	const scenarios = {
		low: weightedAverage * 0.85,
		mid: weightedAverage,
		high: weightedAverage * 1.25,
	}

	console.log("📊 Reported MVIC calculation:")
	console.log(`   Available MVIC values: ${mvicValues.length}/${recentYears.length} years`)
	console.log(`   Weighted average: ${formatMillions(weightedAverage)}`)
	console.log(
		`   Scenarios: Low=${formatMillions(scenarios.low)}, Mid=${formatMillions(
			scenarios.mid
		)}, High=${formatMillions(scenarios.high)}`
	)

	return {
		hasReportedData: true,
		scenarios,
		source,
	}
}

// Run the script
main()
	.catch(console.error)
	.finally(() => prisma.$disconnect())
