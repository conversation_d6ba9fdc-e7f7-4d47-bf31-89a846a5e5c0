import { PrismaClient } from "@prisma/client"
import * as fs from "fs"
import * as readline from "readline"
import { yearDataDb } from "../modules/year-data/year-data.db"
import { organizationsDb } from "../modules/organizations/organizations.db"
import { yearDataCreateDTO, YearDataCreateDTO } from "../modules/year-data/year-data.schemas"

const prisma = new PrismaClient()

interface FinancialDataInput {
	organizationId?: string
	organizationName?: string
	year: number
	MVIC: number
	EBITDA: number
	OPM: number
	netSales: number
}

interface BulkFinancialDataInput {
	organizationId?: string
	organizationName?: string
	yearData: Array<{
		year: number
		MVIC: number
		EBITDA: number
		OPM: number
		netSales: number
	}>
}

interface FinancialDataConfig {
	entries: (FinancialDataInput | BulkFinancialDataInput)[]
}

// Helper function to create readline interface
function createReadlineInterface() {
	return readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	})
}

// Helper function to ask a question and get user input
function askQuestion(rl: readline.Interface, question: string): Promise<string> {
	return new Promise(resolve => {
		rl.question(question, answer => {
			resolve(answer.trim())
		})
	})
}

async function main() {
	const args = process.argv.slice(2)

	if (args.length === 0) {
		console.log("💰 Financial Data Manager")
		console.log("\nUsage:")
		console.log(
			"  npm run financial:add <orgId|orgName> <year> <MVIC> <EBITDA> <OPM> <netSales>"
		)
		console.log(
			"  npm run financial:add-json <filename>         - Add financial data from JSON"
		)
		console.log("  npm run financial:add-json <filename> --dry-run - Preview without creating")
		console.log("  npm run financial:template [filename]         - Generate template JSON")
		console.log(
			"  npm run financial:interactive                 - Add financial data interactively"
		)
		console.log(
			"  npm run financial:list [orgId|orgName]        - List financial data for organization(s)"
		)
		console.log("  npm run financial:list-orgs                   - List all organizations")
		console.log("\nExamples:")
		console.log("  npm run financial:add 'Tech Corp' 2023 5000000 2000000 15.5 12000000")
		console.log("  npm run financial:add abc123-def456 2023 5000000 2000000 15.5 12000000")
		return
	}

	const command = args[0]

	if (command === "template") {
		const filename = args[1] || "financial-data-template.json"
		await generateTemplate(filename)
	} else if (command === "list-orgs") {
		await listOrganizations()
	} else if (command === "list") {
		const orgIdentifier = args[1]
		await listFinancialData(orgIdentifier)
	} else if (command === "interactive") {
		await interactiveMode()
	} else if (command === "add-json") {
		const filename = args[1]
		const dryRun = args.includes("--dry-run")

		if (!filename) {
			console.error("❌ Please provide a JSON file")
			process.exit(1)
		}

		await addFinancialDataFromJson(filename, dryRun)
	} else if (command === "add") {
		// Direct add: npm run financial:add <orgId|orgName> <year> <MVIC> <EBITDA> <OPM> <netSales>
		if (args.length < 6) {
			console.error(
				"❌ Please provide: <orgId|orgName> <year> <MVIC> <EBITDA> <OPM> <netSales>"
			)
			process.exit(1)
		}

		const [, orgIdentifier, yearStr, mvicStr, ebitdaStr, opmStr, netSalesStr] = args

		const year = parseInt(yearStr)
		const MVIC = parseFloat(mvicStr)
		const EBITDA = parseFloat(ebitdaStr)
		const OPM = parseFloat(opmStr)
		const netSales = parseFloat(netSalesStr)

		if (isNaN(year) || isNaN(MVIC) || isNaN(EBITDA) || isNaN(OPM) || isNaN(netSales)) {
			console.error("❌ Invalid numeric values provided")
			process.exit(1)
		}

		await addSingleFinancialData({
			organizationId: orgIdentifier.length === 36 ? orgIdentifier : undefined,
			organizationName: orgIdentifier.length !== 36 ? orgIdentifier : undefined,
			year,
			MVIC,
			EBITDA,
			OPM,
			netSales,
		})
	} else {
		console.error("❌ Unknown command:", command)
		process.exit(1)
	}
}

async function findOrganization(identifier: string) {
	// Try to find by ID first (if it looks like a UUID)
	if (identifier.length === 36 && identifier.includes("-")) {
		const org = await organizationsDb.getOrganization(identifier)
		if (org) return org
	}

	// Try to find by name
	const org = await prisma.organization.findFirst({
		where: { name: { contains: identifier, mode: "insensitive" } },
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})

	return org
}

async function addSingleFinancialData(input: FinancialDataInput) {
	try {
		console.log("💰 Adding financial data...")

		// Find organization
		const orgIdentifier = input.organizationId || input.organizationName!
		const organization = await findOrganization(orgIdentifier)

		if (!organization) {
			console.error(`❌ Organization not found: ${orgIdentifier}`)
			console.log("💡 Use 'npm run financial:list-orgs' to see available organizations")
			process.exit(1)
		}

		console.log(`🏢 Organization: ${organization.name}`)

		// Check if year data already exists
		const existingYearData = await yearDataDb.getYearDataByOrg(organization.id)
		const existingYear = existingYearData.find(yd => yd.year === input.year)

		if (existingYear) {
			console.error(
				`❌ Financial data for year ${input.year} already exists for ${organization.name}`
			)
			console.log(
				`💡 Existing data: MVIC: ${existingYear.MVIC}, EBITDA: ${existingYear.EBITDA}, OPM: ${existingYear.OPM}, Net Sales: ${existingYear.netSales}`
			)
			process.exit(1)
		}

		// Validate data using schema
		const validatedData: YearDataCreateDTO = yearDataCreateDTO.parse({
			orgId: organization.id,
			year: input.year,
			MVIC: input.MVIC,
			EBITDA: input.EBITDA,
			OPM: input.OPM,
			netSales: input.netSales,
		})

		// Create the year data
		const yearData = await yearDataDb.createYearData({
			year: validatedData.year,
			MVIC: validatedData.MVIC,
			EBITDA: validatedData.EBITDA,
			OPM: validatedData.OPM,
			netSales: validatedData.netSales,
			org: {
				connect: { id: organization.id },
			},
		})

		console.log("✅ Financial data added successfully!")
		console.log(`📊 Year: ${yearData.year}`)
		console.log(`💰 MVIC: $${yearData.MVIC.toLocaleString()}`)
		console.log(`📈 EBITDA: $${yearData.EBITDA.toLocaleString()}`)
		console.log(`📊 OPM: ${yearData.OPM}%`)
		console.log(`💵 Net Sales: $${yearData.netSales.toLocaleString()}`)
	} catch (error: any) {
		if (error.name === "ZodError") {
			console.error("❌ Validation error:")
			error.errors.forEach((err: any) => {
				console.error(`  • ${err.path.join(".")}: ${err.message}`)
			})
		} else {
			console.error("❌ Error adding financial data:", error.message)
		}
		process.exit(1)
	}
}

async function listOrganizations() {
	try {
		console.log("🏢 Organizations")
		console.log("=".repeat(80))

		const organizations = await organizationsDb.getOrganizations()

		if (organizations.length === 0) {
			console.log("📭 No organizations found")
			return
		}

		console.log(
			`${"Name".padEnd(30)} ${"Years".padEnd(8)} ${"NAICS".padEnd(10)} ${"Year Data".padEnd(
				12
			)} ${"ID".padEnd(36)}`
		)
		console.log("-".repeat(80))

		for (const org of organizations) {
			const name = org.name.substring(0, 28).padEnd(30)
			const years = `${org.yearsInBusiness}`.padEnd(8)
			const naics = org.NAICSCode.padEnd(10)
			const yearDataCount = `${org.yearData?.length || 0} years`.padEnd(12)
			const id = org.id

			console.log(`${name} ${years} ${naics} ${yearDataCount} ${id}`)
		}

		console.log("-".repeat(80))
		console.log(`📊 Total: ${organizations.length} organizations`)
	} catch (error) {
		console.error("❌ Error listing organizations:", error)
	}
}

async function listFinancialData(orgIdentifier?: string) {
	try {
		if (orgIdentifier) {
			// List financial data for specific organization
			const organization = await findOrganization(orgIdentifier)

			if (!organization) {
				console.error(`❌ Organization not found: ${orgIdentifier}`)
				console.log("💡 Use 'npm run financial:list-orgs' to see available organizations")
				return
			}

			console.log(`💰 Financial Data for: ${organization.name}`)
			console.log("=".repeat(80))

			if (!organization.yearData || organization.yearData.length === 0) {
				console.log("📭 No financial data found for this organization")
				return
			}

			console.log(
				`${"Year".padEnd(8)} ${"MVIC".padEnd(15)} ${"EBITDA".padEnd(15)} ${"OPM".padEnd(
					10
				)} ${"Net Sales".padEnd(15)} ${"ID".padEnd(36)}`
			)
			console.log("-".repeat(80))

			for (const yearData of organization.yearData) {
				const year = `${yearData.year}`.padEnd(8)
				const mvic = `$${yearData.MVIC.toLocaleString()}`.padEnd(15)
				const ebitda = `$${yearData.EBITDA.toLocaleString()}`.padEnd(15)
				const opm = `${yearData.OPM}%`.padEnd(10)
				const netSales = `$${yearData.netSales.toLocaleString()}`.padEnd(15)
				const id = yearData.id

				console.log(`${year} ${mvic} ${ebitda} ${opm} ${netSales} ${id}`)
			}

			console.log("-".repeat(80))
			console.log(`📊 Total: ${organization.yearData.length} years of data`)
		} else {
			// List all financial data across all organizations
			console.log("💰 All Financial Data")
			console.log("=".repeat(100))

			const organizations = await organizationsDb.getOrganizations()
			const orgsWithData = organizations.filter(
				org => org.yearData && org.yearData.length > 0
			)

			if (orgsWithData.length === 0) {
				console.log("📭 No financial data found")
				return
			}

			console.log(
				`${"Organization".padEnd(25)} ${"Year".padEnd(8)} ${"MVIC".padEnd(
					12
				)} ${"EBITDA".padEnd(12)} ${"OPM".padEnd(8)} ${"Net Sales".padEnd(12)}`
			)
			console.log("-".repeat(100))

			for (const org of orgsWithData) {
				for (const yearData of org.yearData!) {
					const orgName = org.name.substring(0, 23).padEnd(25)
					const year = `${yearData.year}`.padEnd(8)
					const mvic = `$${(yearData.MVIC / 1000000).toFixed(1)}M`.padEnd(12)
					const ebitda = `$${(yearData.EBITDA / 1000000).toFixed(1)}M`.padEnd(12)
					const opm = `${yearData.OPM}%`.padEnd(8)
					const netSales = `$${(yearData.netSales / 1000000).toFixed(1)}M`.padEnd(12)

					console.log(`${orgName} ${year} ${mvic} ${ebitda} ${opm} ${netSales}`)
				}
			}

			const totalEntries = orgsWithData.reduce(
				(sum, org) => sum + (org.yearData?.length || 0),
				0
			)
			console.log("-".repeat(100))
			console.log(
				`📊 Total: ${totalEntries} financial data entries across ${orgsWithData.length} organizations`
			)
		}
	} catch (error) {
		console.error("❌ Error listing financial data:", error)
	}
}

async function generateTemplate(filename: string) {
	const template: FinancialDataConfig = {
		entries: [
			{
				organizationName: "Tech Innovators Inc.",
				year: 2023,
				MVIC: 5000000,
				EBITDA: 2000000,
				OPM: 15.5,
				netSales: 12000000,
			},
			{
				organizationId: "your-org-id-here",
				year: 2022,
				MVIC: 4500000,
				EBITDA: 1800000,
				OPM: 14.2,
				netSales: 11000000,
			},
			{
				organizationName: "Growth Corp",
				yearData: [
					{
						year: 2021,
						MVIC: 3000000,
						EBITDA: 1200000,
						OPM: 12.0,
						netSales: 8000000,
					},
					{
						year: 2022,
						MVIC: 3500000,
						EBITDA: 1400000,
						OPM: 13.1,
						netSales: 9500000,
					},
					{
						year: 2023,
						MVIC: 4200000,
						EBITDA: 1680000,
						OPM: 14.8,
						netSales: 11200000,
					},
				],
			},
		],
	}

	fs.writeFileSync(filename, JSON.stringify(template, null, 2))
	console.log(`📄 Template generated: ${filename}`)
	console.log("\n📝 Template includes examples for:")
	console.log("   • Single year entry by organization name")
	console.log("   • Single year entry by organization ID")
	console.log("   • Multiple years for one organization (bulk entry)")
	console.log("\n💰 Financial Data Fields:")
	console.log("   • MVIC: Market Value of Invested Capital")
	console.log("   • EBITDA: Earnings Before Interest, Taxes, Depreciation, and Amortization")
	console.log("   • OPM: Operating Profit Margin (percentage)")
	console.log("   • netSales: Net Sales/Revenue")
	console.log("\n🚀 Next steps:")
	console.log(`   1. Edit ${filename} with your data`)
	console.log(`   2. Run: npm run financial:add-json ${filename}`)
	console.log(`   3. Or preview: npm run financial:add-json ${filename} --dry-run`)
}

async function addFinancialDataFromJson(filePath: string, dryRun: boolean) {
	console.log(`📁 Reading financial data from: ${filePath}`)

	if (!fs.existsSync(filePath)) {
		throw new Error(`File not found: ${filePath}`)
	}

	const fileContent = fs.readFileSync(filePath, "utf-8")
	const config: FinancialDataConfig = JSON.parse(fileContent)

	console.log(`📊 Found ${config.entries.length} entry/entries to process`)

	if (dryRun) {
		console.log("\n🔍 DRY RUN MODE - No data will be created\n")
	}

	for (let i = 0; i < config.entries.length; i++) {
		const entry = config.entries[i]
		console.log(`\n💰 Processing entry ${i + 1}`)

		// Determine if this is a single entry or bulk entry
		if ("yearData" in entry) {
			// Bulk entry
			await processBulkEntry(entry as BulkFinancialDataInput, dryRun)
		} else {
			// Single entry
			await processSingleEntry(entry as FinancialDataInput, dryRun)
		}
	}

	if (dryRun) {
		console.log("\n✅ Dry run completed - no data was actually created")
		console.log("Remove --dry-run flag to actually create the financial data")
	} else {
		console.log("\n✅ All financial data added successfully!")
	}
}

async function processSingleEntry(entry: FinancialDataInput, dryRun: boolean) {
	try {
		// Find organization
		const orgIdentifier = entry.organizationId || entry.organizationName!
		const organization = await findOrganization(orgIdentifier)

		if (!organization) {
			console.log(`   ❌ Organization not found: ${orgIdentifier}`)
			return
		}

		console.log(`   🏢 Organization: ${organization.name}`)

		// Check if year data already exists
		const existingYearData = await yearDataDb.getYearDataByOrg(organization.id)
		const existingYear = existingYearData.find(yd => yd.year === entry.year)

		if (existingYear) {
			console.log(`   ⚠️  Financial data for year ${entry.year} already exists`)
			return
		}

		if (dryRun) {
			console.log(`   ✅ Would create financial data for year ${entry.year}`)
			console.log(`   💰 MVIC: $${entry.MVIC.toLocaleString()}`)
			console.log(`   📈 EBITDA: $${entry.EBITDA.toLocaleString()}`)
			console.log(`   📊 OPM: ${entry.OPM}%`)
			console.log(`   💵 Net Sales: $${entry.netSales.toLocaleString()}`)
			return
		}

		// Validate and create
		const validatedData: YearDataCreateDTO = yearDataCreateDTO.parse({
			orgId: organization.id,
			year: entry.year,
			MVIC: entry.MVIC,
			EBITDA: entry.EBITDA,
			OPM: entry.OPM,
			netSales: entry.netSales,
		})

		const yearData = await yearDataDb.createYearData({
			year: validatedData.year,
			MVIC: validatedData.MVIC,
			EBITDA: validatedData.EBITDA,
			OPM: validatedData.OPM,
			netSales: validatedData.netSales,
			org: {
				connect: { id: organization.id },
			},
		})

		console.log(`   ✅ Created financial data for year ${yearData.year}`)
	} catch (error: any) {
		if (error.name === "ZodError") {
			console.log(`   ❌ Validation error:`)
			error.errors.forEach((err: any) => {
				console.log(`      • ${err.path.join(".")}: ${err.message}`)
			})
		} else {
			console.log(`   ❌ Error: ${error.message}`)
		}
	}
}

async function processBulkEntry(entry: BulkFinancialDataInput, dryRun: boolean) {
	try {
		// Find organization
		const orgIdentifier = entry.organizationId || entry.organizationName!
		const organization = await findOrganization(orgIdentifier)

		if (!organization) {
			console.log(`   ❌ Organization not found: ${orgIdentifier}`)
			return
		}

		console.log(`   🏢 Organization: ${organization.name}`)
		console.log(`   📊 Processing ${entry.yearData.length} years of data`)

		// Check for existing data
		const existingYearData = await yearDataDb.getYearDataByOrg(organization.id)
		const existingYears = existingYearData.map(yd => yd.year)

		const newYearData = entry.yearData.filter(yd => !existingYears.includes(yd.year))
		const skippedYears = entry.yearData.filter(yd => existingYears.includes(yd.year))

		if (skippedYears.length > 0) {
			console.log(
				`   ⚠️  Skipping ${skippedYears.length} years (already exist): ${skippedYears
					.map(y => y.year)
					.join(", ")}`
			)
		}

		if (newYearData.length === 0) {
			console.log(`   ℹ️  No new data to add`)
			return
		}

		if (dryRun) {
			console.log(`   ✅ Would create ${newYearData.length} years of financial data`)
			newYearData.forEach(yd => {
				console.log(
					`      • ${
						yd.year
					}: MVIC $${yd.MVIC.toLocaleString()}, EBITDA $${yd.EBITDA.toLocaleString()}`
				)
			})
			return
		}

		// Validate and create bulk data
		const validatedYearData = newYearData.map(yd =>
			yearDataCreateDTO.parse({
				orgId: organization.id,
				year: yd.year,
				MVIC: yd.MVIC,
				EBITDA: yd.EBITDA,
				OPM: yd.OPM,
				netSales: yd.netSales,
			})
		)

		const result = await yearDataDb.bulkCreateYearData(
			organization.id,
			validatedYearData.map(vd => ({
				year: vd.year,
				MVIC: vd.MVIC,
				EBITDA: vd.EBITDA,
				OPM: vd.OPM,
				netSales: vd.netSales,
			}))
		)

		console.log(`   ✅ Created ${result.count} years of financial data`)
	} catch (error: any) {
		if (error.name === "ZodError") {
			console.log(`   ❌ Validation error:`)
			error.errors.forEach((err: any) => {
				console.log(`      • ${err.path.join(".")}: ${err.message}`)
			})
		} else {
			console.log(`   ❌ Error: ${error.message}`)
		}
	}
}

async function interactiveMode() {
	const rl = createReadlineInterface()

	try {
		console.log("💰 Interactive Financial Data Creator")
		console.log("=".repeat(50))
		console.log("This will guide you through adding financial data step by step.")
		console.log("Type 'exit' at any time to quit, 'list' to see organizations.\n")

		// Get organization
		const orgIdentifier = await askQuestion(rl, "🏢 Organization name or ID: ")
		if (orgIdentifier.toLowerCase() === "exit") return

		if (orgIdentifier.toLowerCase() === "list") {
			await listOrganizations()
			return
		}

		const organization = await findOrganization(orgIdentifier)
		if (!organization) {
			console.log(`❌ Organization not found: ${orgIdentifier}`)
			console.log("💡 Use 'list' to see available organizations")
			return
		}

		console.log(`✅ Organization: ${organization.name}`)

		// Show existing financial data
		const existingData = await yearDataDb.getYearDataByOrg(organization.id)
		if (existingData.length > 0) {
			console.log("\n📊 Existing financial data:")
			existingData.forEach(yd => {
				console.log(
					`   • ${
						yd.year
					}: MVIC $${yd.MVIC.toLocaleString()}, EBITDA $${yd.EBITDA.toLocaleString()}`
				)
			})
		}

		// Get financial data
		console.log("\n💰 Enter financial data:")

		const yearStr = await askQuestion(rl, "📅 Year: ")
		if (yearStr.toLowerCase() === "exit") return
		const year = parseInt(yearStr)

		if (isNaN(year) || year < 1900 || year > 2100) {
			console.log("❌ Invalid year. Please enter a year between 1900 and 2100.")
			return
		}

		// Check if year already exists
		const existingYear = existingData.find(yd => yd.year === year)
		if (existingYear) {
			console.log(`❌ Financial data for year ${year} already exists`)
			return
		}

		const mvicStr = await askQuestion(rl, "💰 MVIC (Market Value of Invested Capital): $")
		if (mvicStr.toLowerCase() === "exit") return
		const MVIC = parseFloat(mvicStr.replace(/[,$]/g, ""))

		const ebitdaStr = await askQuestion(rl, "📈 EBITDA: $")
		if (ebitdaStr.toLowerCase() === "exit") return
		const EBITDA = parseFloat(ebitdaStr.replace(/[,$]/g, ""))

		const opmStr = await askQuestion(rl, "📊 OPM (Operating Profit Margin) %: ")
		if (opmStr.toLowerCase() === "exit") return
		const OPM = parseFloat(opmStr.replace(/%/g, ""))

		const netSalesStr = await askQuestion(rl, "💵 Net Sales: $")
		if (netSalesStr.toLowerCase() === "exit") return
		const netSales = parseFloat(netSalesStr.replace(/[,$]/g, ""))

		if (isNaN(MVIC) || isNaN(EBITDA) || isNaN(OPM) || isNaN(netSales)) {
			console.log("❌ Invalid numeric values. Please enter valid numbers.")
			return
		}

		// Confirm data
		console.log("\n📋 Review your data:")
		console.log(`   🏢 Organization: ${organization.name}`)
		console.log(`   📅 Year: ${year}`)
		console.log(`   💰 MVIC: $${MVIC.toLocaleString()}`)
		console.log(`   📈 EBITDA: $${EBITDA.toLocaleString()}`)
		console.log(`   📊 OPM: ${OPM}%`)
		console.log(`   💵 Net Sales: $${netSales.toLocaleString()}`)

		const confirm = await askQuestion(rl, "\n✅ Create this financial data? (y/N): ")
		if (confirm.toLowerCase() !== "y" && confirm.toLowerCase() !== "yes") {
			console.log("❌ Cancelled")
			return
		}

		// Create the data
		await addSingleFinancialData({
			organizationId: organization.id,
			year,
			MVIC,
			EBITDA,
			OPM,
			netSales,
		})
	} catch (error) {
		console.error("❌ Error in interactive mode:", error)
	} finally {
		rl.close()
	}
}

// Run the script
main()
	.catch(console.error)
	.finally(() => prisma.$disconnect())
