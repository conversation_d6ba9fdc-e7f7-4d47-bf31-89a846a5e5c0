import { PrismaClient } from "@prisma/client"
import * as fs from "fs"
import * as readline from "readline"
import { SURVEY_QUESTIONS } from "../../prisma/survey"

const prisma = new PrismaClient()

interface ResponseInput {
	organizationName: string
	organizationAge?: number // yearsInBusiness, defaults to 5
	responses: Array<{
		questionOrder: number // Question order (1, 2, 3, etc.) - maps to survey.ts array index
		selectedOption?: string // Display text of selected option
		selectedOptions?: string[] // Array of display texts for multi-select
		freeResponseText?: string // For free response questions
	}>
}

interface ResponsesConfig {
	evaluations: ResponseInput[]
}

// Helper functions to make working with the survey easier
function getQuestionByOrder(order: number) {
	return SURVEY_QUESTIONS[order - 1] // Convert 1-based to 0-based index
}

function validateResponse(response: ResponseInput["responses"][0]): string | null {
	const question = getQuestionByOrder(response.questionOrder)
	if (!question)
		return `Question order ${response.questionOrder} not found in survey (max: ${SURVEY_QUESTIONS.length})`

	if (question.type === "FREE_RESPONSE") {
		if (!response.freeResponseText && response.freeResponseText !== "") {
			return `Question ${response.questionOrder} is free response but no freeResponseText provided`
		}
	} else if (question.type === "RADIO") {
		if (!response.selectedOption) {
			return `Question ${response.questionOrder} is single-select but no selectedOption provided`
		}
		const validOptions = question.options.map(opt => opt.displayText)
		if (!validOptions.includes(response.selectedOption)) {
			return `Invalid option "${response.selectedOption}" for question ${
				response.questionOrder
			}. Valid options: ${validOptions.join(", ")}`
		}
	} else if (question.type === "MULTISELECT") {
		if (!response.selectedOptions || response.selectedOptions.length === 0) {
			return `Question ${response.questionOrder} is multi-select but no selectedOptions provided`
		}
		const validOptions = question.options.map(opt => opt.displayText)
		const invalidOptions = response.selectedOptions.filter(opt => !validOptions.includes(opt))
		if (invalidOptions.length > 0) {
			return `Invalid options for question ${response.questionOrder}: ${invalidOptions.join(
				", "
			)}. Valid options: ${validOptions.join(", ")}`
		}
	}

	return null // Valid
}

async function main() {
	const args = process.argv.slice(2)

	if (args.length === 0) {
		console.log("📋 Survey Response Manager")
		console.log("\nUsage:")
		console.log("  npm run responses:template [filename]     - Generate template JSON")
		console.log("  npm run responses:add <filename>         - Add responses from JSON")
		console.log("  npm run responses:add <filename> --dry-run - Preview without creating")
		console.log("  npm run responses:list-questions         - List all survey questions")
		console.log("  npm run responses:interactive            - Add responses interactively")
		return
	}

	const command = args[0]

	if (command === "template") {
		const filename = args[1] || "responses-template.json"
		await generateTemplate(filename)
	} else if (command === "list-questions") {
		await listQuestions()
	} else if (command === "interactive") {
		await interactiveMode()
	} else if (command === "add") {
		const filename = args[1]
		const dryRun = args.includes("--dry-run")

		if (!filename) {
			console.error("❌ Please provide a JSON file")
			process.exit(1)
		}

		await addResponsesFromJson(filename, dryRun)
	} else {
		console.error("❌ Unknown command:", command)
		process.exit(1)
	}
}

async function listQuestions() {
	console.log("📋 Survey Questions Reference")
	console.log("=".repeat(50))

	// Group questions by type for easier reference
	const questionsByType = {
		RADIO: SURVEY_QUESTIONS.filter(q => q.type === "RADIO"),
		MULTISELECT: SURVEY_QUESTIONS.filter(q => q.type === "MULTISELECT"),
		FREE_RESPONSE: SURVEY_QUESTIONS.filter(q => q.type === "FREE_RESPONSE"),
	}

	Object.entries(questionsByType).forEach(([type, questions]) => {
		if (questions.length === 0) return

		console.log(`\n🔹 ${type} Questions:`)
		questions.forEach(q => {
			const questionOrder = SURVEY_QUESTIONS.indexOf(q) + 1
			console.log(`\n   Q${questionOrder}: ${q.question}`)
			if (q.type !== "FREE_RESPONSE") {
				console.log(`   Options:`)
				q.options.forEach(opt => {
					console.log(`     • "${opt.displayText}"`)
				})
			} else {
				console.log(`   Type: Free text response`)
			}
		})
	})

	console.log("\n🎯 Special Scoring Questions:")
	const q130Order = SURVEY_QUESTIONS.findIndex(q => q.id === "q130") + 1
	const q131Order = SURVEY_QUESTIONS.findIndex(q => q.id === "q131") + 1
	const q134Order = SURVEY_QUESTIONS.findIndex(q => q.id === "q134") + 1
	const q36Order = SURVEY_QUESTIONS.findIndex(q => q.id === "q36") + 1

	console.log(
		`   • Q${q130Order} & Q${q131Order}: Multiplier relationship (Q${q131Order} multiplies Q${q130Order})`
	)
	console.log(`   • Q${q134Order}: Age-dependent scoring (<3yrs, 4-6yrs, >7yrs)`)
	console.log(`   • Q${q36Order}: Free response for responsibility tracking`)

	console.log("\n💡 Usage in JSON:")
	console.log('   Single-select: { "questionOrder": 1, "selectedOption": "Yes" }')
	console.log(
		'   Multi-select:  { "questionOrder": 2, "selectedOptions": ["Option 1", "Option 2"] }'
	)
	console.log(
		`   Free response: { "questionOrder": ${q36Order}, "freeResponseText": "Your text here" }`
	)
}

// Helper function to create readline interface
function createReadlineInterface() {
	return readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	})
}

// Helper function to ask a question and get user input
function askQuestion(rl: readline.Interface, question: string): Promise<string> {
	return new Promise(resolve => {
		rl.question(question, answer => {
			resolve(answer.trim())
		})
	})
}

async function interactiveMode() {
	const rl = createReadlineInterface()

	try {
		console.log("🎯 Interactive Response Creator")
		console.log("=".repeat(50))
		console.log("This will guide you through creating survey responses step by step.")
		console.log(
			"Type 'exit' at any time to quit, 'help' for commands, or 'list' to see questions.\n"
		)

		// Get organization info
		const orgName = await askQuestion(rl, "📋 Organization name: ")
		if (orgName.toLowerCase() === "exit") return

		const orgAgeStr = await askQuestion(rl, "🎂 Organization age in years (default: 5): ")
		if (orgAgeStr.toLowerCase() === "exit") return
		const orgAge = orgAgeStr ? parseInt(orgAgeStr) || 5 : 5

		console.log(`\n✅ Organization: ${orgName} (${orgAge} years old)`)
		console.log("\n🎯 Starting survey - going through questions one by one...")
		console.log(
			"💡 Commands: 'skip' to skip a question, 'jump <number>' to jump to specific question, 'done' to finish"
		)

		// Collect responses
		const responses: ResponseInput["responses"] = []
		let currentQuestionOrder = 1

		while (currentQuestionOrder <= SURVEY_QUESTIONS.length) {
			console.log("\n" + "─".repeat(50))

			const command = await askQuestion(rl, `\n📝 Q${currentQuestionOrder} (or command): `)

			if (command.toLowerCase() === "exit" || command.toLowerCase() === "done") {
				break
			} else if (command.toLowerCase() === "help") {
				console.log("\n💡 Available commands:")
				console.log("  skip        - Skip current question and move to next")
				console.log("  jump <num>  - Jump to specific question number")
				console.log("  list        - Show all survey questions")
				console.log("  done        - Finish and create responses")
				console.log("  exit        - Quit without saving")
				console.log("  <enter>     - Answer the current question")
				continue
			} else if (command.toLowerCase() === "skip") {
				console.log(`⏭️  Skipped Q${currentQuestionOrder}`)
				currentQuestionOrder++
				continue
			} else if (command.toLowerCase().startsWith("jump ")) {
				const jumpTo = parseInt(command.split(" ")[1])
				if (jumpTo >= 1 && jumpTo <= SURVEY_QUESTIONS.length) {
					currentQuestionOrder = jumpTo
					console.log(`🔄 Jumped to Q${currentQuestionOrder}`)
					continue
				} else {
					console.log(`❌ Invalid question number. Use 1-${SURVEY_QUESTIONS.length}`)
					continue
				}
			} else if (command.toLowerCase() === "list") {
				await showQuestionsList()
				continue
			} else if (command.trim() === "" || command.toLowerCase() === "answer") {
				// Answer the current question
				const response = await collectResponseForQuestion(rl, currentQuestionOrder)
				if (response) {
					responses.push(response)
					console.log(`✅ Added response for Q${currentQuestionOrder}`)
					currentQuestionOrder++
				}
				continue
			} else {
				// Try to parse as question number for jumping
				const questionOrder = parseInt(command)
				if (questionOrder >= 1 && questionOrder <= SURVEY_QUESTIONS.length) {
					currentQuestionOrder = questionOrder
					console.log(`🔄 Jumped to Q${currentQuestionOrder}`)
					continue
				} else {
					console.log(
						"❌ Invalid command. Type 'help' for available commands or press Enter to answer current question."
					)
					continue
				}
			}
		}

		if (responses.length === 0) {
			console.log("No responses collected. Exiting.")
			return
		}

		// Create the evaluation
		console.log(`\n🚀 Creating evaluation with ${responses.length} responses...`)

		const evaluation: ResponseInput = {
			organizationName: orgName,
			organizationAge: orgAge,
			responses,
		}

		await createEvaluationWithResponses(evaluation)
		console.log("✅ Evaluation created successfully!")
	} catch (error) {
		console.error("❌ Error in interactive mode:", error)
	} finally {
		rl.close()
	}
}

async function showQuestionsList() {
	console.log("\n📋 Available Questions:")
	console.log("=".repeat(30))

	SURVEY_QUESTIONS.forEach((q, index) => {
		const questionOrder = index + 1
		const shortQuestion =
			q.question.length > 60 ? q.question.substring(0, 60) + "..." : q.question
		console.log(`Q${questionOrder}: ${shortQuestion} [${q.type}]`)
	})

	console.log(`\nTotal: ${SURVEY_QUESTIONS.length} questions`)
	console.log("Type a question number (e.g., '1') to answer it.")
}

async function collectResponseForQuestion(
	rl: readline.Interface,
	questionOrder: number
): Promise<ResponseInput["responses"][0] | null> {
	const question = getQuestionByOrder(questionOrder)
	if (!question) {
		console.log(`❌ Question Q${questionOrder} not found`)
		return null
	}

	console.log(`\n📋 Q${questionOrder}: ${question.question}`)
	console.log(`📝 Type: ${question.type}`)

	if (question.type === "FREE_RESPONSE") {
		const text = await askQuestion(rl, "💬 Your response: ")
		if (text.toLowerCase() === "skip") {
			console.log("⏭️  Skipped question")
			return null
		}
		return {
			questionOrder,
			freeResponseText: text,
		}
	} else if (question.type === "RADIO") {
		console.log("\n📋 Options:")
		question.options.forEach((opt, index) => {
			console.log(`  ${index + 1}. ${opt.displayText}`)
		})

		const choice = await askQuestion(rl, "🎯 Select option (number or 'skip'): ")
		if (choice.toLowerCase() === "skip") {
			console.log("⏭️  Skipped question")
			return null
		}

		const optionIndex = parseInt(choice) - 1

		if (optionIndex >= 0 && optionIndex < question.options.length) {
			return {
				questionOrder,
				selectedOption: question.options[optionIndex].displayText,
			}
		} else {
			console.log("❌ Invalid option number. Question skipped.")
			return null
		}
	} else if (question.type === "MULTISELECT") {
		console.log("\n📋 Options (you can select multiple):")
		question.options.forEach((opt, index) => {
			console.log(`  ${index + 1}. ${opt.displayText}`)
		})

		const choices = await askQuestion(
			rl,
			"🎯 Select options (comma-separated numbers, e.g., '1,3,5' or 'skip'): "
		)

		if (choices.toLowerCase() === "skip") {
			console.log("⏭️  Skipped question")
			return null
		}

		const optionIndices = choices
			.split(",")
			.map(s => parseInt(s.trim()) - 1)
			.filter(i => i >= 0 && i < question.options.length)

		if (optionIndices.length > 0) {
			return {
				questionOrder,
				selectedOptions: optionIndices.map(i => question.options[i].displayText),
			}
		} else {
			console.log("❌ No valid options selected. Question skipped.")
			return null
		}
	}

	return null
}

async function generateTemplate(filename: string) {
	// Find key questions by their IDs and get their order positions
	const keyQuestionIds = ["q1", "q2", "q36", "q130", "q131", "q134"]
	const keyQuestionOrders = keyQuestionIds
		.map(id => {
			const index = SURVEY_QUESTIONS.findIndex(q => q.id === id)
			return index >= 0 ? index + 1 : null
		})
		.filter(order => order !== null) as number[]

	const template: ResponsesConfig = {
		evaluations: [
			{
				organizationName: "Tech Startup Inc",
				organizationAge: 2, // Will trigger <3 years scoring for Q134
				responses: [
					{
						questionOrder: keyQuestionOrders[0] || 1, // q1
						selectedOption: "Yes",
					},
					{
						questionOrder: keyQuestionOrders[1] || 2, // q2
						selectedOptions: ["Name, Logo and Tagline", "Marketing Activities"],
					},
					{
						questionOrder: keyQuestionOrders[2] || 36, // q36
						freeResponseText: "John Smith, Marketing Director",
					},
					{
						questionOrder: keyQuestionOrders[3] || 130, // q130
						selectedOption: "Yes, Regional list",
					},
					{
						questionOrder: keyQuestionOrders[4] || 131, // q131
						selectedOption: "#1 industry",
					},
					{
						questionOrder: keyQuestionOrders[5] || 134, // q134
						selectedOption: "A. Leadership defines the Brand",
					},
				],
			},
			{
				organizationName: "Established Corp",
				organizationAge: 10, // Will trigger >7 years scoring for Q134
				responses: [
					{
						questionOrder: keyQuestionOrders[0] || 1, // q1
						selectedOption: "Yes",
					},
					{
						questionOrder: keyQuestionOrders[5] || 134, // q134
						selectedOption: "B. Brand shapes the Leadership",
					},
				],
			},
		],
	}

	fs.writeFileSync(filename, JSON.stringify(template, null, 2))
	console.log(`📄 Template generated: ${filename}`)
	console.log("\n📝 Template includes examples for:")
	console.log("   • Single-select questions (q1, q130, q131, q134)")
	console.log("   • Multi-select questions (q2)")
	console.log("   • Free response questions (q36)")
	console.log("   • Organizations with different ages (affects Q134 scoring)")
	console.log("\n🚀 Next steps:")
	console.log(`   1. Edit ${filename} with your data`)
	console.log(`   2. Run: npm run responses:add ${filename}`)
	console.log(`   3. Or preview: npm run responses:add ${filename} --dry-run`)
}

async function addResponsesFromJson(filePath: string, dryRun: boolean) {
	console.log(`📁 Reading responses from: ${filePath}`)

	if (!fs.existsSync(filePath)) {
		throw new Error(`File not found: ${filePath}`)
	}

	const fileContent = fs.readFileSync(filePath, "utf-8")
	const config: ResponsesConfig = JSON.parse(fileContent)

	console.log(`📊 Found ${config.evaluations.length} evaluation(s) to process`)

	if (dryRun) {
		console.log("\n🔍 DRY RUN MODE - No data will be created\n")
	}

	for (let i = 0; i < config.evaluations.length; i++) {
		const evaluation = config.evaluations[i]
		console.log(`\n🏢 Processing evaluation ${i + 1}: ${evaluation.organizationName}`)

		// Validate all responses using survey structure
		const validationErrors: string[] = []
		evaluation.responses.forEach(response => {
			const error = validateResponse(response)
			if (error) validationErrors.push(error)
		})

		if (validationErrors.length > 0) {
			console.log(`   ❌ Validation errors:`)
			validationErrors.forEach(error => console.log(`      • ${error}`))
			continue
		}

		if (dryRun) {
			console.log(`   ✅ All ${evaluation.responses.length} responses are valid`)
			console.log(`   🎂 Organization age: ${evaluation.organizationAge || 5} years`)

			// Show some response details with question info
			evaluation.responses.slice(0, 3).forEach(r => {
				const question = getQuestionByOrder(r.questionOrder)
				const questionText = question
					? question.question.substring(0, 50) + "..."
					: "Unknown question"

				if (r.selectedOption) {
					console.log(`   • Q${r.questionOrder} (${questionText}): "${r.selectedOption}"`)
				} else if (r.selectedOptions) {
					console.log(
						`   • Q${r.questionOrder} (${questionText}): [${r.selectedOptions.join(
							", "
						)}]`
					)
				} else if (r.freeResponseText) {
					console.log(
						`   • Q${r.questionOrder} (${questionText}): "${r.freeResponseText}"`
					)
				}
			})

			if (evaluation.responses.length > 3) {
				console.log(`   • ... and ${evaluation.responses.length - 3} more responses`)
			}
			continue
		}

		await createEvaluationWithResponses(evaluation)
	}

	if (dryRun) {
		console.log("\n✅ Dry run completed - no data was actually created")
		console.log("Remove --dry-run flag to actually create the responses")
	} else {
		console.log("\n✅ All responses added successfully!")
		console.log("💡 You can now complete evaluations via the API to calculate scores")
	}
}

async function createEvaluationWithResponses(input: ResponseInput) {
	// Find or create organization
	let organization = await prisma.organization.findFirst({
		where: { name: input.organizationName },
	})

	if (!organization) {
		// Create a basic organization if it doesn't exist
		const defaultVar = await prisma.var.findFirst()
		if (!defaultVar) {
			throw new Error("No vars found in database. Please run the seed script first.")
		}

		organization = await prisma.organization.create({
			data: {
				name: input.organizationName,
				varId: defaultVar.id,
				NAICSCode: "000000",
				yearsInBusiness: input.organizationAge || 5,
				valuation: 1000000,
			},
		})
		console.log(
			`   🏢 Created organization: ${organization.name} (${organization.yearsInBusiness} years old)`
		)
	} else {
		// Update age if provided
		if (
			input.organizationAge !== undefined &&
			organization.yearsInBusiness !== input.organizationAge
		) {
			await prisma.organization.update({
				where: { id: organization.id },
				data: { yearsInBusiness: input.organizationAge },
			})
			console.log(`   🔄 Updated organization age: ${input.organizationAge} years`)
		}
	}

	// Find the survey
	const survey = await prisma.survey.findFirst({
		include: {
			questions: {
				include: {
					questionOption: true,
				},
				orderBy: { order: "asc" },
			},
		},
	})

	if (!survey) {
		throw new Error("No survey found in database. Please run the seed script first.")
	}

	// Create evaluation
	const evaluation = await prisma.evaluation.create({
		data: {
			surveyId: survey.id,
			organizationId: organization.id,
		},
	})

	console.log(`   📋 Created evaluation: ${evaluation.id}`)

	// Create responses
	let successCount = 0
	for (const responseInput of input.responses) {
		const success = await createResponse(evaluation.id, responseInput, survey.questions)
		if (success) successCount++
	}

	console.log(`   ✅ Created ${successCount}/${input.responses.length} responses`)
}

async function createResponse(
	evaluationId: string,
	responseInput: ResponseInput["responses"][0],
	questions: any[]
): Promise<boolean> {
	try {
		// Use survey.ts structure to find the question
		const surveyQuestion = getQuestionByOrder(responseInput.questionOrder)
		if (!surveyQuestion) {
			console.log(`   ⚠️  Question not found in survey.ts: Q${responseInput.questionOrder}`)
			return false
		}

		// Find the corresponding database question by order
		const question = questions.find(q => q.order === responseInput.questionOrder)

		if (!question) {
			console.log(`   ⚠️  Question not found in database: Q${responseInput.questionOrder}`)
			return false
		}

		const responseData: any = {
			evaluationId,
			surveyQuestionId: question.id,
		}

		if (question.type === "FREE_RESPONSE") {
			responseData.freeResponseText = responseInput.freeResponseText || ""
		} else if (question.type === "RADIO" && responseInput.selectedOption) {
			const option = question.questionOption.find(
				(opt: any) => opt.displayText === responseInput.selectedOption
			)
			if (option) {
				responseData.selectedOptionId = option.id
			} else {
				console.log(
					`   ⚠️  Option not found for Q${responseInput.questionOrder}: "${responseInput.selectedOption}"`
				)
				return false
			}
		} else if (question.type === "MULTISELECT" && responseInput.selectedOptions) {
			const optionIds = responseInput.selectedOptions
				.map(optText => {
					const option = question.questionOption.find(
						(opt: any) => opt.displayText === optText
					)
					if (!option) {
						console.log(
							`   ⚠️  Option not found for Q${responseInput.questionOrder}: "${optText}"`
						)
					}
					return option?.id
				})
				.filter(Boolean)

			if (optionIds.length === 0) {
				console.log(`   ⚠️  No valid options found for Q${responseInput.questionOrder}`)
				return false
			}

			// For multi-select, create the response first, then connect the options
			const response = await prisma.response.create({
				data: responseData,
			})

			await prisma.response.update({
				where: { id: response.id },
				data: {
					selectedOptions: {
						connect: optionIds.map(id => ({ id })),
					},
				},
			})
			return true
		}

		await prisma.response.create({
			data: responseData,
		})
		return true
	} catch (error) {
		console.log(`   ❌ Error creating response for Q${responseInput.questionOrder}:`, error)
		return false
	}
}

// Run the script
main()
	.catch(console.error)
	.finally(() => prisma.$disconnect())
