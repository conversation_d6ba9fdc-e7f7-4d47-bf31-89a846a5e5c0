import { PrismaClient } from "@prisma/client"
import {
	calculateBrandStrengthScore,
	validateSurveyCompletion,
} from "../modules/evaluations/services/brand-strength-calculator"

const prisma = new PrismaClient()

async function main() {
	const args = process.argv.slice(2)
	const command = args[0]

	if (!command) {
		console.log("🧮 Brand Strength Calculator")
		console.log("\nUsage:")
		console.log(
			"  npm run brand-strength:calculate <evaluationId>  - Calculate score for specific evaluation"
		)
		console.log(
			"  npm run brand-strength:calculate-all             - Calculate scores for all evaluations"
		)
		console.log(
			"  npm run brand-strength:list                      - List evaluations with current scores"
		)
		console.log(
			"  npm run brand-strength:update <evaluationId>     - Calculate and update score in database"
		)
		console.log(
			"  npm run brand-strength:update-all                - Calculate and update all evaluation scores"
		)
		return
	}

	if (command === "calculate") {
		const evaluationId = args[1]
		if (!evaluationId) {
			console.error("❌ Please provide an evaluation ID")
			process.exit(1)
		}
		await calculateScoreForEvaluation(evaluationId, false)
	} else if (command === "calculate-all") {
		await calculateScoresForAllEvaluations(false)
	} else if (command === "list") {
		await listEvaluationsWithScores()
	} else if (command === "update") {
		const evaluationId = args[1]
		if (!evaluationId) {
			console.error("❌ Please provide an evaluation ID")
			process.exit(1)
		}
		await calculateScoreForEvaluation(evaluationId, true)
	} else if (command === "update-all") {
		await calculateScoresForAllEvaluations(true)
	} else {
		console.error("❌ Unknown command:", command)
		process.exit(1)
	}
}

async function calculateScoreForEvaluation(evaluationId: string, updateDatabase: boolean = false) {
	try {
		console.log(
			`\n🧮 ${
				updateDatabase ? "Calculating and updating" : "Calculating"
			} brand strength for evaluation: ${evaluationId}`
		)

		// Fetch evaluation with responses and all necessary data
		const evaluation = await prisma.evaluation.findUnique({
			where: { id: evaluationId },
			include: {
				organization: true,
				responses: {
					include: {
						selectedOption: true,
						selectedOptions: true,
						surveyQuestion: {
							include: {
								questionOption: true,
							},
						},
					},
				},
			},
		})

		if (!evaluation) {
			console.error(`❌ Evaluation not found: ${evaluationId}`)
			return
		}

		console.log(`📋 Organization: ${evaluation.organization?.name}`)
		console.log(`📊 Responses: ${evaluation.responses.length}`)

		// Transform responses to the format expected by the calculator
		const transformedResponses = evaluation.responses.map(response => ({
			id: response.id,
			surveyQuestionId: response.surveyQuestionId,
			selectedOption: response.selectedOption
				? {
						id: response.selectedOption.id,
						scoreIfSelected: response.selectedOption.scoreIfSelected,
						scoreIfUnselected: response.selectedOption.scoreIfUnselected,
				  }
				: null,
			selectedOptions:
				response.selectedOptions?.map(option => ({
					id: option.id,
					scoreIfSelected: option.scoreIfSelected,
					scoreIfUnselected: option.scoreIfUnselected,
				})) || [],
			surveyQuestion: {
				id: response.surveyQuestion.id,
				type: response.surveyQuestion.type,
				group: response.surveyQuestion.group,
				questionOption: response.surveyQuestion.questionOption.map(option => ({
					id: option.id,
					scoreIfSelected: option.scoreIfSelected,
					scoreIfUnselected: option.scoreIfUnselected,
				})),
			},
		}))

		// Calculate the brand strength score
		const brandStrengthScore = calculateBrandStrengthScore(transformedResponses)

		console.log(`\n🎯 Brand Strength Score: ${brandStrengthScore}/1000`)
		console.log(`📈 Percentage: ${(brandStrengthScore / 10).toFixed(1)}%`)

		// Show score interpretation
		let interpretation = ""
		if (brandStrengthScore >= 800) {
			interpretation = "🏆 Excellent - Very strong brand"
		} else if (brandStrengthScore >= 700) {
			interpretation = "🌟 Good - Strong brand with room for improvement"
		} else if (brandStrengthScore >= 600) {
			interpretation = "📈 Fair - Moderate brand strength"
		} else if (brandStrengthScore >= 400) {
			interpretation = "⚠️  Needs Work - Weak brand strength"
		} else {
			interpretation = "🚨 Critical - Very weak brand, immediate attention needed"
		}
		console.log(`💡 ${interpretation}`)

		// Get all question IDs to validate completion
		const allQuestions = await prisma.surveyQuestion.findMany({
			select: { id: true },
		})
		const allQuestionIds = allQuestions.map(q => q.id)

		// Validate survey completion
		const validation = validateSurveyCompletion(transformedResponses, allQuestionIds)

		if (validation.isComplete) {
			console.log("✅ Survey is complete - all questions answered")
		} else {
			console.log(
				`⚠️  Survey incomplete - ${validation.missingQuestions.length} questions missing`
			)
			console.log("💡 Score calculated based on answered questions only")
		}

		// Update database if requested
		if (updateDatabase) {
			await prisma.evaluation.update({
				where: { id: evaluationId },
				data: { brandStrengthScore },
			})
			console.log("💾 Score updated in database")
		}

		return brandStrengthScore
	} catch (error) {
		console.error("❌ Error calculating brand strength:", error)
		throw error
	}
}

async function calculateScoresForAllEvaluations(updateDatabase: boolean = false) {
	try {
		console.log(
			`\n🧮 ${
				updateDatabase ? "Calculating and updating" : "Calculating"
			} brand strength for all evaluations...`
		)

		const evaluations = await prisma.evaluation.findMany({
			include: {
				organization: true,
				responses: {
					include: {
						selectedOption: true,
						selectedOptions: true,
						surveyQuestion: {
							include: {
								questionOption: true,
							},
						},
					},
				},
			},
		})

		if (evaluations.length === 0) {
			console.log("📭 No evaluations found")
			return
		}

		console.log(`📊 Found ${evaluations.length} evaluations`)

		const results = []

		for (let i = 0; i < evaluations.length; i++) {
			const evaluation = evaluations[i]
			console.log(
				`\n[${i + 1}/${evaluations.length}] Processing: ${evaluation.organization?.name}`
			)

			try {
				const score = await calculateScoreForEvaluation(evaluation.id, updateDatabase)
				results.push({
					id: evaluation.id,
					organizationName: evaluation.organization?.name,
					score,
					responseCount: evaluation.responses.length,
				})
			} catch (error) {
				console.error(`❌ Failed to process evaluation ${evaluation.id}:`, error)
				results.push({
					id: evaluation.id,
					organizationName: evaluation.organization?.name,
					score: null,
					responseCount: evaluation.responses.length,
					error: true,
				})
			}
		}

		// Summary
		console.log("\n" + "=".repeat(60))
		console.log("📊 SUMMARY")
		console.log("=".repeat(60))

		const successful = results.filter(r => r.score !== null)
		const failed = results.filter(r => r.error)

		console.log(`✅ Successfully processed: ${successful.length}`)
		console.log(`❌ Failed: ${failed.length}`)

		if (successful.length > 0) {
			const avgScore =
				successful.reduce((sum, r) => sum + (r.score || 0), 0) / successful.length
			const maxScore = Math.max(...successful.map(r => r.score || 0))
			const minScore = Math.min(...successful.map(r => r.score || 0))

			console.log(`📈 Average Score: ${avgScore.toFixed(1)}/1000`)
			console.log(`🏆 Highest Score: ${maxScore}/1000`)
			console.log(`📉 Lowest Score: ${minScore}/1000`)
		}
	} catch (error) {
		console.error("❌ Error processing evaluations:", error)
		throw error
	}
}

async function listEvaluationsWithScores() {
	try {
		console.log("\n📊 Evaluations with Brand Strength Scores")
		console.log("=".repeat(80))

		const evaluations = await prisma.evaluation.findMany({
			include: {
				organization: true,
				responses: true,
			},
			orderBy: {
				brandStrengthScore: "desc",
			},
		})

		if (evaluations.length === 0) {
			console.log("📭 No evaluations found")
			return
		}

		console.log(
			`${"Organization".padEnd(30)} ${"Score".padEnd(10)} ${"Responses".padEnd(
				10
			)} ${"Status".padEnd(15)} ${"ID".padEnd(36)}`
		)
		console.log("-".repeat(80))

		for (const evaluation of evaluations) {
			const orgName = evaluation.organization?.name.substring(0, 28).padEnd(30)
			const score =
				evaluation.brandStrengthScore !== null
					? `${evaluation.brandStrengthScore}/1000`.padEnd(10)
					: "Not calc".padEnd(10)
			const responseCount = `${evaluation.responses.length}`.padEnd(10)
			const status = evaluation.brandStrengthScore !== null ? "✅ Calculated" : "⏳ Pending"
			const statusPadded = status.padEnd(15)
			const id = evaluation.id

			console.log(`${orgName} ${score} ${responseCount} ${statusPadded} ${id}`)
		}

		const calculated = evaluations.filter(e => e.brandStrengthScore !== null)
		const pending = evaluations.filter(e => e.brandStrengthScore === null)

		console.log("-".repeat(80))
		console.log(
			`📊 Total: ${evaluations.length} | ✅ Calculated: ${calculated.length} | ⏳ Pending: ${pending.length}`
		)
	} catch (error) {
		console.error("❌ Error listing evaluations:", error)
		throw error
	}
}

main()
	.catch(console.error)
	.finally(() => prisma.$disconnect())
