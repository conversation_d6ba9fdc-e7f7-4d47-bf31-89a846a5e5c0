import { PrismaClient } from "@prisma/client"
import * as fs from "fs"

import { Command } from "commander"

const prisma = new PrismaClient()

interface ResponseInput {
	organizationName: string
	responses: Array<{
		questionId: string // From survey.ts (e.g., "q1", "q2")
		selectedOption?: string // Display text of selected option
		selectedOptions?: string[] // Array of display texts for multi-select
		freeResponseText?: string // For free response questions
	}>
}

interface ResponsesConfig {
	evaluations: ResponseInput[]
}

const program = new Command()

program.name("add-responses").description("Add survey responses to the database").version("1.0.0")

program
	.command("from-json")
	.description("Add responses from a JSON file")
	.argument("<file>", "JSON file containing responses")
	.option("-d, --dry-run", "Show what would be created without actually creating it")
	.action(async (file: string, options: { dryRun?: boolean }) => {
		try {
			await addResponsesFromJson(file, options.dryRun || false)
		} catch (error) {
			console.error("Error:", error)
			process.exit(1)
		}
	})

program
	.command("interactive")
	.description("Add responses interactively")
	.action(async () => {
		try {
			await addResponsesInteractively()
		} catch (error) {
			console.error("Error:", error)
			process.exit(1)
		}
	})

program
	.command("generate-template")
	.description("Generate a template JSON file")
	.argument("[filename]", "Output filename", "responses-template.json")
	.action(async (filename: string) => {
		await generateTemplate(filename)
	})

async function addResponsesFromJson(filePath: string, dryRun: boolean) {
	console.log(`📁 Reading responses from: ${filePath}`)

	if (!fs.existsSync(filePath)) {
		throw new Error(`File not found: ${filePath}`)
	}

	const fileContent = fs.readFileSync(filePath, "utf-8")
	const config: ResponsesConfig = JSON.parse(fileContent)

	console.log(`📊 Found ${config.evaluations.length} evaluation(s) to process`)

	for (let i = 0; i < config.evaluations.length; i++) {
		const evaluation = config.evaluations[i]
		console.log(`\n🏢 Processing evaluation ${i + 1}: ${evaluation.organizationName}`)

		if (dryRun) {
			console.log(`   📝 Would create ${evaluation.responses.length} responses`)
			continue
		}

		await createEvaluationWithResponses(evaluation)
	}

	if (dryRun) {
		console.log("\n✅ Dry run completed - no data was actually created")
	} else {
		console.log("\n✅ All responses added successfully!")
	}
}

async function createEvaluationWithResponses(input: ResponseInput) {
	// Find or create organization
	let organization = await prisma.organization.findFirst({
		where: { name: input.organizationName },
	})

	if (!organization) {
		// Create a basic organization if it doesn't exist
		const defaultVar = await prisma.var.findFirst()
		if (!defaultVar) {
			throw new Error("No vars found in database. Please run the seed script first.")
		}

		organization = await prisma.organization.create({
			data: {
				name: input.organizationName,
				varId: defaultVar.id,
				NAICSCode: "000000",
				yearsInBusiness: 5,
				valuation: 1000000,
			},
		})
		console.log(`   🏢 Created organization: ${organization.name}`)
	}

	// Find the survey
	const survey = await prisma.survey.findFirst({
		include: {
			questions: {
				include: {
					questionOption: true,
				},
			},
		},
	})

	if (!survey) {
		throw new Error("No survey found in database. Please run the seed script first.")
	}

	// Create evaluation
	const evaluation = await prisma.evaluation.create({
		data: {
			surveyId: survey.id,
			organizationId: organization.id,
		},
	})

	console.log(`   📋 Created evaluation: ${evaluation.id}`)

	// Create responses
	for (const responseInput of input.responses) {
		await createResponse(evaluation.id, responseInput, survey.questions)
	}

	console.log(`   ✅ Created ${input.responses.length} responses`)
}

async function createResponse(
	evaluationId: string,
	responseInput: ResponseInput["responses"][0],
	questions: any[]
) {
	// Find the question by the original ID from survey.ts
	const question = questions.find(q => {
		// We need to match by question text since the IDs are different in the database
		// This is a simplified approach - in production you might want a better mapping
		return q.order === parseInt(responseInput.questionId.replace("q", ""))
	})

	if (!question) {
		console.log(`   ⚠️  Question not found: ${responseInput.questionId}`)
		return
	}

	const responseData: any = {
		evaluationId,
		surveyQuestionId: question.id,
	}

	if (question.type === "FREE_RESPONSE") {
		responseData.freeResponseText = responseInput.freeResponseText || ""
	} else if (question.type === "RADIO" && responseInput.selectedOption) {
		const option = question.questionOption.find(
			(opt: any) => opt.displayText === responseInput.selectedOption
		)
		if (option) {
			responseData.selectedOptionId = option.id
		}
	} else if (question.type === "MULTISELECT" && responseInput.selectedOptions) {
		const optionIds = responseInput.selectedOptions
			.map(optText => {
				const option = question.questionOption.find(
					(opt: any) => opt.displayText === optText
				)
				return option?.id
			})
			.filter(Boolean)

		// For multi-select, we need to create the response first, then connect the options
		const response = await prisma.response.create({
			data: responseData,
		})

		if (optionIds.length > 0) {
			await prisma.response.update({
				where: { id: response.id },
				data: {
					selectedOptions: {
						connect: optionIds.map(id => ({ id })),
					},
				},
			})
		}
		return
	}

	await prisma.response.create({
		data: responseData,
	})
}

async function addResponsesInteractively() {
	console.log("🎯 Interactive Response Creator")
	console.log("This feature will be implemented in a future version.")
	console.log("For now, please use the JSON file approach with 'generate-template' command.")
}

async function generateTemplate(filename: string) {
	const template: ResponsesConfig = {
		evaluations: [
			{
				organizationName: "Example Company Inc",
				responses: [
					{
						questionId: "q1",
						selectedOption: "Yes",
					},
					{
						questionId: "q2",
						selectedOptions: ["Name, Logo and Tagline", "Marketing Activities"],
					},
					{
						questionId: "q36",
						freeResponseText: "John Smith, Marketing Director",
					},
				],
			},
			{
				organizationName: "Another Company LLC",
				responses: [
					{
						questionId: "q1",
						selectedOption: "No",
					},
					{
						questionId: "q134",
						selectedOption: "A. Leadership defines the Brand",
					},
				],
			},
		],
	}

	fs.writeFileSync(filename, JSON.stringify(template, null, 2))
	console.log(`📄 Template generated: ${filename}`)
	console.log("\nTemplate includes examples for:")
	console.log("- Single-select questions (RADIO)")
	console.log("- Multi-select questions (MULTISELECT)")
	console.log("- Free response questions (FREE_RESPONSE)")
	console.log("\nEdit the file and run: npm run add-responses from-json " + filename)
}

// Main execution
if (require.main === module) {
	program.parse()
}
