{"name": "bw-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "prisma:generate": "prisma generate", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build --remove-orphans", "valuate": "ts-node src/valuations.ts", "responses:template": "ts-node src/scripts/add-responses-simple.ts template", "responses:add": "ts-node src/scripts/add-responses-simple.ts add", "responses:list": "ts-node src/scripts/add-responses-simple.ts list-questions", "responses:interactive": "ts-node src/scripts/add-responses-simple.ts interactive", "brand-strength:calculate": "ts-node src/scripts/calculate-brand-strength.ts calculate", "brand-strength:calculate-all": "ts-node src/scripts/calculate-brand-strength.ts calculate-all", "brand-strength:list": "ts-node src/scripts/calculate-brand-strength.ts list", "brand-strength:update": "ts-node src/scripts/calculate-brand-strength.ts update", "brand-strength:update-all": "ts-node src/scripts/calculate-brand-strength.ts update-all", "financial:add": "ts-node src/scripts/add-financial-data.ts add", "financial:add-json": "ts-node src/scripts/add-financial-data.ts add-json", "financial:template": "ts-node src/scripts/add-financial-data.ts template", "financial:interactive": "ts-node src/scripts/add-financial-data.ts interactive", "financial:list": "ts-node src/scripts/add-financial-data.ts list", "financial:list-orgs": "ts-node src/scripts/add-financial-data.ts list-orgs", "valuation:calculate": "ts-node src/scripts/calculate-valuation.ts calculate", "valuation:calculate-all": "ts-node src/scripts/calculate-valuation.ts calculate-all", "valuation:update": "ts-node src/scripts/calculate-valuation.ts update", "valuation:update-all": "ts-node src/scripts/calculate-valuation.ts update-all", "valuation:list": "ts-node src/scripts/calculate-valuation.ts list", "valuation:dealstats": "ts-node src/scripts/calculate-valuation.ts dealstats"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "commander": "^12.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "prisma": "^6.9.0", "xlsx": "^0.18.5", "zod": "^3.25.57"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}