# CLI Quick Reference

## Financial Data Management

### Basic Commands

```bash
# Show help
ts-node src/scripts/add-financial-data.ts

# Interactive mode (recommended for beginners)
npm run financial:interactive

# Quick single entry
npm run financial:add "Org Name" 2023 5000000 2000000 15.5 12000000
#                     <org>     <year> <MVIC>  <EBITDA> <OPM> <netSales>
```

### JSON Import/Export

```bash
# Generate template
npm run financial:template [filename]

# Import data
npm run financial:add-json filename.json

# Preview import (dry run)
npm run financial:add-json filename.json --dry-run
```

### Listing Data

```bash
# List all organizations
npm run financial:list-orgs

# List all financial data
npm run financial:list

# List data for specific organization
npm run financial:list "Organization Name"
npm run financial:list org-uuid-here
```

## Survey Response Management

### Basic Commands

```bash
# Interactive mode
npm run responses:interactive

# Generate template
npm run responses:template [filename]

# Import responses
npm run responses:add filename.json

# Preview import
npm run responses:add filename.json --dry-run

# List survey questions
npm run responses:list
```

## Brand Strength Calculation

### Calculate Scores

```bash
# Calculate for specific evaluation
npm run brand-strength:calculate <evaluationId>

# Calculate for all evaluations
npm run brand-strength:calculate-all

# Update database with calculated scores
npm run brand-strength:update <evaluationId>
npm run brand-strength:update-all

# List evaluations with scores
npm run brand-strength:list
```

## Organization Valuation

### Basic Commands

```bash
# Calculate valuation for organization
npm run valuation:calculate "Organization Name"
npm run valuation:calculate org-uuid-here

# Calculate and save to database
npm run valuation:update "Organization Name"

# Calculate for all organizations
npm run valuation:calculate-all
npm run valuation:update-all
```

### Status and Information

```bash
# List organizations with valuation status
npm run valuation:list

# Check DealStats files and organization readiness
npm run valuation:dealstats
```

## Data Fields Reference

### Financial Data Fields

| Field    | Description                                                     | Example  |
| -------- | --------------------------------------------------------------- | -------- |
| MVIC     | Market Value of Invested Capital                                | 5000000  |
| EBITDA   | Earnings Before Interest, Taxes, Depreciation, and Amortization | 2000000  |
| OPM      | Operating Profit Margin (percentage)                            | 15.5     |
| netSales | Net Sales/Revenue                                               | 12000000 |

### Organization Identification

-   **By Name**: Partial matching, case-insensitive
    -   `"Tech Corp"` matches "Tech Corporation Inc."
-   **By ID**: Exact UUID match
    -   `"abc123-def456-ghi789-012345"`

## JSON Templates

### Single Financial Entry

```json
{
	"entries": [
		{
			"organizationName": "Tech Corp",
			"year": 2023,
			"MVIC": 5000000,
			"EBITDA": 2000000,
			"OPM": 15.5,
			"netSales": 12000000
		}
	]
}
```

### Bulk Financial Entry

```json
{
	"entries": [
		{
			"organizationName": "Growth Corp",
			"yearData": [
				{
					"year": 2021,
					"MVIC": 3000000,
					"EBITDA": 1200000,
					"OPM": 12.0,
					"netSales": 8000000
				},
				{
					"year": 2022,
					"MVIC": 3500000,
					"EBITDA": 1400000,
					"OPM": 13.1,
					"netSales": 9500000
				}
			]
		}
	]
}
```

### Survey Response Entry

```json
{
	"evaluations": [
		{
			"organizationName": "Tech Startup Inc",
			"organizationAge": 2,
			"responses": [
				{
					"questionOrder": 1,
					"selectedOption": "Yes"
				},
				{
					"questionOrder": 2,
					"selectedOptions": ["Option 1", "Option 2"]
				},
				{
					"questionOrder": 36,
					"freeResponseText": "John Smith, Marketing Director"
				}
			]
		}
	]
}
```

## Common Workflows

### Adding Financial Data for New Organization

1. `npm run financial:list-orgs` - Check if organization exists
2. `npm run financial:interactive` - Add data interactively
3. `npm run financial:list "Org Name"` - Verify data was added

### Bulk Import Financial Data

1. `npm run financial:template data.json` - Generate template
2. Edit `data.json` with your data
3. `npm run financial:add-json data.json --dry-run` - Preview
4. `npm run financial:add-json data.json` - Import

### Complete Brand Evaluation Workflow

1. `npm run responses:interactive` - Add survey responses
2. `npm run brand-strength:calculate <evaluationId>` - Calculate score
3. `npm run brand-strength:update <evaluationId>` - Save to database

## Tips

-   **Always use --dry-run** first for bulk imports
-   **Use interactive mode** when learning or for single entries
-   **Check existing data** before adding to avoid duplicates
-   **Use partial organization names** for easier identification
-   **Generate templates** to understand the expected JSON format
