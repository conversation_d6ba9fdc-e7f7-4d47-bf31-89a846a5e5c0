# Financial Data Management

This document explains how to add and manage financial data for organizations using the CLI tool.

## Overview

The Financial Data Manager allows you to add yearly financial data (MVIC, EBITDA, OPM, Net Sales) to organizations in the database. It supports multiple input methods and provides comprehensive validation and error handling.

## Quick Start

### Option A: Interactive Mode (Recommended for Single Entries)

```bash
npm run financial:interactive
```

This starts an interactive session where you can add financial data step by step through the command line.

### Option B: Direct Command Line (Quick Single Entry)

```bash
npm run financial:add "Organization Name" 2023 5000000 2000000 15.5 12000000
```

Parameters: `<orgId|orgName> <year> <MVIC> <EBITDA> <OPM> <netSales>`

### Option C: JSON File Mode (Recommended for Bulk Data)

#### 1. Generate a Template

```bash
npm run financial:template
```

This creates `financial-data-template.json` with examples for different entry types.

#### 2. Edit the Template

Edit the generated JSON file with your financial data.

#### 3. Import the Data

```bash
npm run financial:add-json financial-data-template.json
```

Or preview without creating:

```bash
npm run financial:add-json financial-data-template.json --dry-run
```

## Available Commands

### Data Management Commands

| Command | Description | Example |
|---------|-------------|---------|
| `npm run financial:add` | Add single financial data entry | `npm run financial:add "Tech Corp" 2023 5000000 2000000 15.5 12000000` |
| `npm run financial:add-json` | Import from JSON file | `npm run financial:add-json data.json` |
| `npm run financial:interactive` | Interactive data entry | `npm run financial:interactive` |
| `npm run financial:template` | Generate JSON template | `npm run financial:template my-data.json` |

### Listing Commands

| Command | Description | Example |
|---------|-------------|---------|
| `npm run financial:list-orgs` | List all organizations | `npm run financial:list-orgs` |
| `npm run financial:list` | List all financial data | `npm run financial:list` |
| `npm run financial:list "Org Name"` | List data for specific org | `npm run financial:list "Tech Corp"` |

## Financial Data Fields

| Field | Description | Example |
|-------|-------------|---------|
| **MVIC** | Market Value of Invested Capital | 5000000 |
| **EBITDA** | Earnings Before Interest, Taxes, Depreciation, and Amortization | 2000000 |
| **OPM** | Operating Profit Margin (percentage) | 15.5 |
| **netSales** | Net Sales/Revenue | 12000000 |

## JSON File Format

### Single Year Entries

```json
{
  "entries": [
    {
      "organizationName": "Tech Innovators Inc.",
      "year": 2023,
      "MVIC": 5000000,
      "EBITDA": 2000000,
      "OPM": 15.5,
      "netSales": 12000000
    },
    {
      "organizationId": "abc123-def456-ghi789",
      "year": 2022,
      "MVIC": 4500000,
      "EBITDA": 1800000,
      "OPM": 14.2,
      "netSales": 11000000
    }
  ]
}
```

### Bulk Entry (Multiple Years for One Organization)

```json
{
  "entries": [
    {
      "organizationName": "Growth Corp",
      "yearData": [
        {
          "year": 2021,
          "MVIC": 3000000,
          "EBITDA": 1200000,
          "OPM": 12.0,
          "netSales": 8000000
        },
        {
          "year": 2022,
          "MVIC": 3500000,
          "EBITDA": 1400000,
          "OPM": 13.1,
          "netSales": 9500000
        }
      ]
    }
  ]
}
```

## Organization Identification

You can identify organizations in two ways:

1. **By Name**: Use the organization name (case-insensitive partial matching)
   - Example: `"Tech Corp"` will match "Tech Corporation Inc."

2. **By ID**: Use the exact organization UUID
   - Example: `"abc123-def456-ghi789-012345"`

## Features

### Validation

- **Data Types**: All numeric fields are validated for proper format
- **Year Range**: Years must be between 1900 and 2100
- **Required Fields**: All financial fields are required
- **Duplicate Prevention**: Prevents adding data for years that already exist

### Error Handling

- **Organization Not Found**: Clear error messages with suggestions
- **Duplicate Data**: Warns when trying to add data for existing years
- **Invalid Data**: Detailed validation error messages
- **File Errors**: Helpful messages for missing or malformed JSON files

### Dry Run Mode

Use `--dry-run` flag to preview what would be created without actually creating it:

```bash
npm run financial:add-json data.json --dry-run
```

### Interactive Features

- **Organization Lookup**: Type 'list' to see all available organizations
- **Existing Data Display**: Shows current financial data for the organization
- **Data Confirmation**: Review all data before creation
- **Input Validation**: Real-time validation with helpful error messages

## Examples

### Adding Data for Multiple Organizations

```bash
# Generate template
npm run financial:template bulk-data.json

# Edit bulk-data.json with your data
# Then import
npm run financial:add-json bulk-data.json
```

### Quick Single Entry

```bash
npm run financial:add "Acme Corp" 2023 8000000 3200000 18.5 15000000
```

### Checking Existing Data

```bash
# List all organizations
npm run financial:list-orgs

# Check specific organization's data
npm run financial:list "Acme Corp"

# See all financial data
npm run financial:list
```

## Tips

1. **Use Interactive Mode** for learning and single entries
2. **Use JSON Mode** for bulk imports and repeatable processes
3. **Always use --dry-run** first when importing large datasets
4. **Check existing data** before adding to avoid duplicates
5. **Use organization names** for easier identification (partial matching supported)

## Troubleshooting

### Common Issues

**Organization Not Found**
- Use `npm run financial:list-orgs` to see available organizations
- Check spelling and try partial names
- Verify the organization exists in the database

**Duplicate Year Data**
- Use `npm run financial:list "Org Name"` to see existing years
- Remove duplicate years from your JSON file
- The tool will skip duplicates and continue with new data

**Invalid JSON**
- Validate your JSON syntax using a JSON validator
- Check for missing commas, brackets, or quotes
- Use the template as a reference for correct format

**Validation Errors**
- Ensure all numeric fields contain valid numbers
- Check that years are in the valid range (1900-2100)
- Verify all required fields are present
