# Brand Strength Calculator CLI

This document explains how to use the CLI tool for calculating brand strength scores based on survey evaluations.

## Quick Start

### Calculate Score for Single Evaluation
```bash
npm run brand-strength:calculate <evaluationId>
```

### Calculate and Update Score in Database
```bash
npm run brand-strength:update <evaluationId>
```

### List All Evaluations with Scores
```bash
npm run brand-strength:list
```

## Commands Reference

### Calculate Score (Read-Only)
```bash
npm run brand-strength:calculate <evaluationId>
```
- Calculates brand strength score for a specific evaluation
- Shows detailed breakdown and validation
- Does NOT update the database
- Perfect for testing and verification

### Calculate All Scores (Read-Only)
```bash
npm run brand-strength:calculate-all
```
- Calculates scores for all evaluations in the database
- Shows summary statistics
- Does NOT update the database
- Useful for analysis and reporting

### List Evaluations
```bash
npm run brand-strength:list
```
- Shows all evaluations with their current brand strength scores
- Displays organization name, score, response count, and status
- Sorted by score (highest first)
- Identifies which evaluations need score calculation

### Update Single Score
```bash
npm run brand-strength:update <evaluationId>
```
- Calculates brand strength score for a specific evaluation
- Updates the `brandStrengthScore` field in the database
- Shows calculation details and confirmation

### Update All Scores
```bash
npm run brand-strength:update-all
```
- Calculates and updates scores for all evaluations
- Processes evaluations one by one with progress indicators
- Shows summary statistics after completion
- Safe to run multiple times (recalculates existing scores)

## Example Usage

### 1. Check Current Scores
```bash
npm run brand-strength:list
```

Output:
```
📊 Evaluations with Brand Strength Scores
================================================================================
Organization                   Score      Responses  Status          ID
--------------------------------------------------------------------------------
Tech Startup Inc              750/1000   45         ✅ Calculated   abc123...
Marketing Agency              Not calc   23         ⏳ Pending      def456...
Consulting Firm               680/1000   38         ✅ Calculated   ghi789...
--------------------------------------------------------------------------------
📊 Total: 3 | ✅ Calculated: 2 | ⏳ Pending: 1
```

### 2. Calculate Score for Specific Evaluation
```bash
npm run brand-strength:calculate abc123-def456-ghi789
```

Output:
```
🧮 Calculating brand strength for evaluation: abc123-def456-ghi789

📋 Organization: Tech Startup Inc
📊 Responses: 45

🎯 Brand Strength Score: 750/1000
📈 Percentage: 75.0%
✅ Survey is complete - all questions answered
```

### 3. Update Score in Database
```bash
npm run brand-strength:update abc123-def456-ghi789
```

Output:
```
🧮 Calculating and updating brand strength for evaluation: abc123-def456-ghi789

📋 Organization: Tech Startup Inc
📊 Responses: 45

🎯 Brand Strength Score: 750/1000
📈 Percentage: 75.0%
✅ Survey is complete - all questions answered
💾 Score updated in database
```

### 4. Calculate All Scores
```bash
npm run brand-strength:calculate-all
```

Output:
```
🧮 Calculating brand strength for all evaluations...
📊 Found 3 evaluations

[1/3] Processing: Tech Startup Inc
🎯 Brand Strength Score: 750/1000

[2/3] Processing: Marketing Agency
🎯 Brand Strength Score: 620/1000

[3/3] Processing: Consulting Firm
🎯 Brand Strength Score: 680/1000

============================================================
📊 SUMMARY
============================================================
✅ Successfully processed: 3
❌ Failed: 0
📈 Average Score: 683.3/1000
🏆 Highest Score: 750/1000
📉 Lowest Score: 620/1000
```

## How Brand Strength Calculation Works

### Scoring Logic
The brand strength calculator uses the scoring system defined in the survey questions:

1. **RADIO Questions (Single Select)**:
   - Adds `scoreIfSelected` for the chosen option
   - Adds `scoreIfUnselected` for all other options

2. **MULTISELECT Questions**:
   - Adds `scoreIfSelected` for each selected option
   - Adds `scoreIfUnselected` for each unselected option

3. **Final Score**:
   - Calculates percentage: `(totalScore / totalPossibleScore) * 100`
   - Scales to 0-1000 range
   - Rounds to nearest integer

### Validation
- Checks if all required questions have been answered
- Shows warning if survey is incomplete
- Calculates score based on answered questions only

### Database Integration
- Reads evaluation data with all responses and options
- Transforms data to format expected by calculator
- Optionally updates `brandStrengthScore` field in evaluation table

## Tips

- **Use `calculate` first** to verify scores before updating database
- **Run `list` regularly** to see which evaluations need score calculation
- **Use `calculate-all`** for analysis and reporting without database changes
- **Use `update-all`** when you want to recalculate all scores (e.g., after scoring changes)

## Troubleshooting

### "Evaluation not found"
- Check that the evaluation ID is correct
- Ensure the evaluation exists in the database

### "No evaluations found"
- Make sure you have created evaluations with responses
- Check database connection

### Incomplete surveys
- The calculator will still work with partial responses
- Score is calculated based on answered questions only
- Check the validation output to see missing questions

## Integration with Response Management

This tool works seamlessly with the response management system:

1. **Create responses** using `npm run responses:interactive` or `npm run responses:add`
2. **Calculate scores** using `npm run brand-strength:calculate`
3. **Update database** using `npm run brand-strength:update`
4. **Monitor progress** using `npm run brand-strength:list`

The brand strength calculator provides the final step in the evaluation workflow, converting survey responses into actionable brand strength metrics.
