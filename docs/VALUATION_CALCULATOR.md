# Organization Valuation Calculator

This document explains how to calculate brand valuations for organizations using the CLI tool.

## Overview

The Valuation Calculator performs comprehensive brand valuation analysis by combining:
- **Financial Data**: 3+ years of EBITDA, Net Sales, and Operating Profit
- **Industry Benchmarks**: NAICS-specific DealStats multipliers from Excel files
- **Brand Strength**: Survey-based brand strength scores (0-1000)
- **Market Analysis**: Goodwill multipliers and MVIC projections

## Quick Start

### Check Prerequisites

```bash
# List available DealStats files and organization readiness
npm run valuation:dealstats

# List organizations with their valuation status
npm run valuation:list
```

### Calculate Single Valuation

```bash
# Calculate valuation for specific organization
npm run valuation:calculate "Organization Name"
npm run valuation:calculate org-uuid-here

# Calculate and update valuation in database
npm run valuation:update "Organization Name"
```

### Bulk Operations

```bash
# Calculate valuations for all organizations
npm run valuation:calculate-all

# Calculate and update all organization valuations
npm run valuation:update-all
```

## Available Commands

| Command | Description | Example |
|---------|-------------|---------|
| `npm run valuation:calculate` | Calculate valuation for organization | `npm run valuation:calculate "Tech Corp"` |
| `npm run valuation:calculate-all` | Calculate valuations for all organizations | `npm run valuation:calculate-all` |
| `npm run valuation:update` | Calculate and save valuation to database | `npm run valuation:update "Tech Corp"` |
| `npm run valuation:update-all` | Calculate and save all valuations | `npm run valuation:update-all` |
| `npm run valuation:list` | List organizations with valuation status | `npm run valuation:list` |
| `npm run valuation:dealstats` | List available DealStats files | `npm run valuation:dealstats` |

## Requirements

### 1. DealStats Files

Each organization needs a corresponding DealStats Excel file named `{NAICSCODE}.xlsx`:

```
522110.xlsx  # For organizations with NAICS code 522110
332999.xlsx  # For organizations with NAICS code 332999
```

**File Location**: Place files in the project root directory.

**File Format**: Excel files must contain columns:
- `EBITDA` - Earnings Before Interest, Taxes, Depreciation, and Amortization
- `Net Sales` - Revenue/Net Sales
- `Operating Profit` - Operating Profit
- `MVIC Price` - Market Value of Invested Capital
- `Goodwill PPA` - Goodwill Purchase Price Allocation

### 2. Financial Data

Organizations must have **at least 3 years** of financial data:

```bash
# Add financial data if missing
npm run financial:add "Org Name" 2023 5000000 2000000 15.5 12000000
npm run financial:interactive
```

### 3. Brand Strength Evaluation (Optional)

While not required, brand strength evaluations significantly improve valuation accuracy:

```bash
# Add survey responses for brand strength calculation
npm run responses:interactive

# Calculate brand strength score
npm run brand-strength:calculate <evaluationId>
```

**Default**: If no evaluation exists, uses 50% brand strength score.

## Valuation Methodology

### 1. MVIC Projection

Calculates Market Value of Invested Capital using weighted averages:
- **Most Recent Year**: 3x weight
- **Second Year**: 2x weight  
- **Third Year**: 1x weight

Uses three financial metrics:
- EBITDA multipliers
- Net Sales multipliers
- Operating Profit multipliers

### 2. Brand Strength Integration

Converts brand strength score (0-1000) to percentage (0-100%) for calculation.

### 3. Goodwill Analysis

Applies industry-specific goodwill multipliers from DealStats data.

### 4. Final Calculation

```
Brand Valuation = MVIC Scenarios × Brand Strength % × Goodwill Multipliers
```

Provides three scenarios:
- **Low (25th percentile)**: Conservative estimate
- **Mid (50th percentile)**: Most likely estimate  
- **High (75th percentile)**: Optimistic estimate

## Example Output

```
💎 VALUATION RESULTS
============================================================
🏢 Organization: Tech Innovators Inc.
🏷️  NAICS Code: 522110
📊 DealStats File: 522110.xlsx

📈 Projected MVIC Scenarios:
   Low (25th percentile):  45.50M
   Mid (50th percentile):  52.30M
   High (75th percentile): 61.20M

🎯 Brand Strength:
   Score: 750/1000 (75.0%)
   Evaluation ID: eval-123-456

💰 Goodwill Multipliers:
   Low:  0.15x
   Mid:  0.22x
   High: 0.31x

💎 BRAND VALUATION:
   Low:  5.12M
   Mid:  8.63M
   High: 14.28M

💡 Brand Valuation as % of MVIC:
   Low:  11.25%
   Mid:  16.50%
   High: 23.33%
```

## Organization Status Indicators

| Status | Description | Action Required |
|--------|-------------|-----------------|
| ✅ Ready | Has all required data | Can calculate valuation |
| ⚠️ No evaluation | Missing brand strength evaluation | Add survey responses |
| ⚠️ No DealStats | Missing NAICS-specific Excel file | Add {NAICSCODE}.xlsx file |
| ⚠️ No financials | Insufficient financial data | Add 3+ years of financial data |
| ❌ Missing data | Multiple requirements missing | Address all missing requirements |

## Troubleshooting

### Common Issues

**DealStats File Not Found**
```bash
# Check available files and organization NAICS codes
npm run valuation:dealstats

# Ensure file is named correctly: {NAICSCODE}.xlsx
# Place file in project root directory
```

**Insufficient Financial Data**
```bash
# Check current financial data
npm run financial:list "Organization Name"

# Add missing years
npm run financial:interactive
```

**No Brand Strength Evaluation**
```bash
# Add survey responses
npm run responses:interactive

# Calculate brand strength
npm run brand-strength:calculate <evaluationId>
```

**Invalid Excel File Format**
- Ensure Excel file has required column headers
- Check that data is in the first worksheet
- Verify numeric data is properly formatted

### Data Quality

**Outlier Detection**: The system automatically removes statistical outliers using IQR method with 3x sensitivity.

**Missing Data Handling**: Rows with missing MVIC Price are excluded from analysis.

**Validation**: All financial inputs are validated for proper numeric format and reasonable ranges.

## Integration with Other Tools

### Financial Data Management
```bash
# Add financial data first
npm run financial:add "Org Name" 2023 5000000 2000000 15.5 12000000

# Then calculate valuation
npm run valuation:calculate "Org Name"
```

### Brand Strength Evaluation
```bash
# Complete survey evaluation
npm run responses:interactive

# Calculate brand strength
npm run brand-strength:update <evaluationId>

# Calculate valuation with brand strength
npm run valuation:calculate "Org Name"
```

### Complete Workflow
```bash
# 1. Add financial data
npm run financial:interactive

# 2. Add survey responses  
npm run responses:interactive

# 3. Calculate brand strength
npm run brand-strength:update-all

# 4. Calculate valuations
npm run valuation:update-all

# 5. Review results
npm run valuation:list
```

## Tips

1. **Ensure Data Quality**: Verify financial data accuracy before valuation
2. **Use Recent Data**: Most recent 3 years provide best projections
3. **Complete Evaluations**: Brand strength evaluations significantly impact accuracy
4. **Industry Specificity**: Ensure correct NAICS code and corresponding DealStats file
5. **Regular Updates**: Recalculate valuations when new financial data or evaluations are added
