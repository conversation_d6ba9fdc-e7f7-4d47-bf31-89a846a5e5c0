# Survey Response Management

This document explains how to add survey responses to the database using the JSON-based CLI tool.

## Quick Start

### Option A: Interactive Mode (Recommended for Testing)

```bash
npm run responses:interactive
```

This starts an interactive session where you can add responses one by one through the command line.

### Option B: JSON File Mode (Recommended for Bulk Data)

#### 1. List Available Questions (Optional)

```bash
npm run responses:list
```

This shows all survey questions with their IDs, types, and available options.

#### 2. Generate a Template

```bash
npm run responses:template
```

This creates `responses-template.json` with smart examples based on the actual survey structure.

#### 3. Edit the Template

Modify the JSON file with your actual response data. The system will validate against the real survey structure.

#### 4. Add Responses

```bash
# Preview what will be created (dry run) - includes validation
npm run responses:add responses-template.json --dry-run

# Actually create the responses
npm run responses:add responses-template.json
```

## Interactive Mode

The interactive mode provides a guided, step-by-step interface for creating survey responses. Perfect for testing, learning the survey structure, or creating individual evaluations.

### How to Use Interactive Mode

1. **Start the interactive session:**

    ```bash
    npm run responses:interactive
    ```

2. **Follow the sequential survey flow:**
    - Enter organization name and age
    - Survey presents questions one by one in order
    - Press Enter to answer each question, or use commands to navigate

### Interactive Commands

-   **`<Enter>`** - Answer the current question
-   **`skip`** - Skip current question and move to next
-   **`jump <number>`** - Jump to a specific question (e.g., `jump 130`)
-   **`list`** - Show all available questions with their numbers and types
-   **`help`** - Display available commands
-   **`done`** - Finish and create the evaluation
-   **`exit`** - Quit without saving

### Example Interactive Session

```
🎯 Interactive Response Creator
==================================================
📋 Organization name: Tech Startup Inc
🎂 Organization age in years (default: 5): 2

✅ Organization: Tech Startup Inc (2 years old)

🎯 Starting survey - going through questions one by one...
💡 Commands: 'skip' to skip a question, 'jump <number>' to jump to specific question, 'done' to finish

──────────────────────────────────────────────────

📝 Q1 (or command):

📋 Q1: Do you have a brand?
📝 Type: RADIO

📋 Options:
  1. Yes
  2. No

🎯 Select option (number or 'skip'): 1
✅ Added response for Q1

──────────────────────────────────────────────────

📝 Q2 (or command): skip
⏭️  Skipped Q2

──────────────────────────────────────────────────

📝 Q3 (or command): jump 130

🔄 Jumped to Q130

──────────────────────────────────────────────────

📝 Q130 (or command):

📋 Q130: Have you been listed on any "Best of" lists?
📝 Type: RADIO

📋 Options:
  1. No, never listed
  2. Yes, Local list
  3. Yes, Regional list
  4. Yes, National/International list

🎯 Select option (number or 'skip'): 3
✅ Added response for Q130

──────────────────────────────────────────────────

📝 Q131 (or command): done

🚀 Creating evaluation with 2 responses...
✅ Evaluation created successfully!
```

### Benefits of Interactive Mode

-   **Natural survey flow** - Questions presented sequentially like a real survey
-   **Guided experience** - No need to know JSON format or question numbers
-   **Real-time validation** - Invalid options are caught immediately
-   **Flexible navigation** - Skip questions, jump around, or follow the natural order
-   **Question discovery** - See all questions and their options as you go
-   **Safe testing** - Perfect for exploring the survey structure

## JSON Format

### Basic Structure

```json
{
	"evaluations": [
		{
			"organizationName": "Company Name",
			"organizationAge": 5,
			"responses": [
				// Response objects here
			]
		}
	]
}
```

### Response Types

#### Single-Select Questions (RADIO)

```json
{
	"questionOrder": 1,
	"selectedOption": "Yes"
}
```

#### Multi-Select Questions (MULTISELECT)

```json
{
	"questionOrder": 2,
	"selectedOptions": ["Name, Logo and Tagline", "Marketing Activities"]
}
```

#### Free Response Questions (FREE_RESPONSE)

```json
{
	"questionOrder": 36,
	"freeResponseText": "John Smith, Marketing Director"
}
```

## Important Questions with Special Scoring

### Q130 & Q131 (Multiplier Relationship)

These questions work together - Q131 multiplies Q130's score:

```json
{
  "questionOrder": 130,
  "selectedOption": "Yes, Regional list"
},
{
  "questionOrder": 131,
  "selectedOption": "#1 industry"
}
```

**Q130 Options:**

-   "No, never listed" (0 points)
-   "Yes, Local list" (1 point)
-   "Yes, Regional list" (3 points)
-   "Yes, National/International list" (5 points)

**Q131 Options (Multipliers):**

-   "#1 industry" (10x)
-   "#2 industry" (9x)
-   "#3-5 industry" (5x)
-   "#6-10 industry" (2x)
-   "#11 to 100" (1x)
-   "Never Recognized" (0x)

### Q134 (Age-Dependent Scoring)

This question's scoring depends on the organization's age:

```json
{
	"questionOrder": 134,
	"selectedOption": "A. Leadership defines the Brand"
}
```

**Scoring Rules:**

-   **< 3 years**: A = +15, B = 0
-   **4-6 years**: A = +15, B = +15
-   **> 7 years**: A = -15, B = +15

**Options:**

-   "A. Leadership defines the Brand"
-   "B. Brand shapes the Leadership"

## Organization Management

### Creating New Organizations

If an organization doesn't exist, it will be created automatically with:

-   Name: As specified in JSON
-   Age: `organizationAge` field (defaults to 5 years)
-   NAICS Code: "000000" (default)
-   Valuation: $1,000,000 (default)

### Updating Existing Organizations

If an organization exists but has a different age, the age will be updated to match the JSON.

## Commands Reference

### Generate Template

```bash
npm run responses:template [filename]
```

-   Creates a template JSON file
-   Default filename: `responses-template.json`
-   Includes examples for all question types

### Interactive Mode

```bash
npm run responses:interactive
```

-   Guided step-by-step response creation
-   Real-time validation and question discovery
-   Perfect for testing and learning the survey structure
-   No JSON knowledge required

### Add Responses

```bash
npm run responses:add <filename> [--dry-run]
```

-   Reads JSON file and creates responses
-   `--dry-run`: Preview without creating data
-   Shows detailed progress and error messages

## Example Workflow

1. **Generate template:**

    ```bash
    npm run responses:template my-responses.json
    ```

2. **Edit the file** with your data:

    ```json
    {
    	"evaluations": [
    		{
    			"organizationName": "Acme Corp",
    			"organizationAge": 8,
    			"responses": [
    				{
    					"questionOrder": 1,
    					"selectedOption": "Yes"
    				},
    				{
    					"questionOrder": 134,
    					"selectedOption": "B. Brand shapes the Leadership"
    				}
    			]
    		}
    	]
    }
    ```

3. **Preview the changes:**

    ```bash
    npm run responses:add my-responses.json --dry-run
    ```

4. **Create the responses:**

    ```bash
    npm run responses:add my-responses.json
    ```

5. **Complete the evaluation** (via API):
    ```bash
    curl -X POST http://localhost:3000/evaluations/{evaluationId}/complete
    ```

## Tips

-   **Use dry-run first** to verify your data before creating responses
-   **Question orders** use numbers: 1, 2, 3, etc. (use `npm run responses:list` to see all)
-   **Option text must match exactly** what's in the database
-   **Organization age affects Q134 scoring** - set it appropriately
-   **Multi-select questions** require an array of option texts
-   **Free response questions** can have empty text (`""`)

## Troubleshooting

### "Question not found"

-   Check that questionOrder is a valid number (1 to max questions)
-   Use `npm run responses:list` to see all available questions

### "Option not found"

-   Verify option text matches exactly (case-sensitive)
-   Check the survey seed data for correct option text

### "No vars found"

-   Run the database seed script first: `npm run seed`

### "No survey found"

-   Run the database seed script first: `npm run seed`

## New Features (Survey Structure Integration)

### Smart Validation

The system now validates all responses against the actual survey structure from `survey.ts`:

-   **Question validation**: Ensures question IDs exist in the survey
-   **Option validation**: Verifies selected options are valid for each question
-   **Type validation**: Checks that response format matches question type
-   **Detailed error messages**: Shows exactly what's wrong and what options are available

### Enhanced Commands

```bash
# List all questions with their options
npm run responses:list

# Generate smart template based on actual survey
npm run responses:template

# Add with full validation
npm run responses:add file.json --dry-run
```

### Benefits

-   **No more guessing**: See all available questions and options
-   **Catch errors early**: Validation happens before database operations
-   **Always up-to-date**: Uses the actual survey structure from survey.ts
-   **Better error messages**: Know exactly what went wrong and how to fix it
