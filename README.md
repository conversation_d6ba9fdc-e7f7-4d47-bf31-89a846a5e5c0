# BrandWorth Server

A comprehensive platform for brand strength evaluation and financial data management.

## Features

-   **Brand Strength Evaluation**: Survey-based brand strength scoring system
-   **Financial Data Management**: Comprehensive financial data tracking for organizations
-   **Organization Management**: Multi-organization support with detailed profiles
-   **CLI Tools**: Command-line tools for data management and automation

## CLI Tools

### Financial Data Management

Manage yearly financial data (MVIC, EBITDA, OPM, Net Sales) for organizations:

```bash
# Interactive data entry
npm run financial:interactive

# Quick single entry
npm run financial:add "Organization Name" 2023 5000000 2000000 15.5 12000000

# Bulk import from JSON
npm run financial:template        # Generate template
npm run financial:add-json data.json

# List organizations and data
npm run financial:list-orgs       # All organizations
npm run financial:list           # All financial data
npm run financial:list "Org Name" # Specific organization
```

### Survey Response Management

Add and manage survey responses for brand strength evaluation:

```bash
# Interactive response entry
npm run responses:interactive

# Generate response template
npm run responses:template

# Import from JSON
npm run responses:add data.json

# List survey questions
npm run responses:list
```

### Brand Strength Calculation

Calculate and manage brand strength scores:

```bash
# Calculate for specific evaluation
npm run brand-strength:calculate <evaluationId>

# Calculate for all evaluations
npm run brand-strength:calculate-all

# Update scores in database
npm run brand-strength:update <evaluationId>
npm run brand-strength:update-all

# List evaluations with scores
npm run brand-strength:list
```

## Documentation

-   [Financial Data Management](docs/FINANCIAL_DATA_MANAGEMENT.md) - Complete guide for managing financial data
-   [Survey Response Management](docs/RESPONSE_MANAGEMENT.md) - Guide for managing survey responses

## Getting Started

1. Install dependencies:

    ```bash
    npm install
    ```

2. Set up the database:

    ```bash
    npm run prisma:generate
    ```

3. Start the development server:

    ```bash
    npm run dev
    ```

4. Use CLI tools to manage data:
    ```bash
    npm run financial:interactive
    ```
